'use client'

import { useState, useEffect } from 'react'
import { WorkflowDashboard } from '@/components/client/workflow-dashboard'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { Workflow } from '@/types/workflow-types'
import { Play, Settings } from 'lucide-react'
import Link from 'next/link'

/**
 * 客户端工作流页面
 */
export default function ClientWorkflowsPage() {
  const { toast } = useToast()
  const [workflows, setWorkflows] = useState<Workflow[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedWorkflow, setSelectedWorkflow] = useState<string | null>(null)

  // 获取所有已发布的工作流
  const fetchWorkflows = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/client-workflows')
      
      if (!response.ok) {
        throw new Error('获取工作流失败')
      }
      
      const data = await response.json()
      if (data.success) {
        setWorkflows(data.data.workflows)
        
        // 如果有工作流且没有选中的工作流，选择第一个
        if (data.data.workflows.length > 0 && !selectedWorkflow) {
          setSelectedWorkflow(data.data.workflows[0].id)
        }
      } else {
        throw new Error(data.error || '获取工作流失败')
      }
    } catch (error) {
      console.error('获取工作流失败:', error)
      toast({
        title: '错误',
        description: '获取工作流列表失败',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    fetchWorkflows()
  }, [])

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">客户端工作流</h1>
        <Button asChild>
          <Link href="/admin/workflows">
            <Settings className="h-4 w-4 mr-2" />
            管理工作流
          </Link>
        </Button>
      </div>

      <div className="grid gap-6">
        {workflows.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-10">
              <p className="text-muted-foreground mb-4">
                {loading ? '加载中...' : '暂无可用的工作流'}
              </p>
              {!loading && (
                <Button asChild>
                  <Link href="/admin/workflows/new">创建新工作流</Link>
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <>
            <Tabs defaultValue="list" className="w-full">
              <TabsList>
                <TabsTrigger value="list">工作流列表</TabsTrigger>
                <TabsTrigger value="dashboard">执行仪表板</TabsTrigger>
              </TabsList>
              
              <TabsContent value="list" className="mt-4">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {workflows.map((workflow) => (
                    <Card key={workflow.id} className="overflow-hidden">
                      <CardHeader className="pb-2">
                        <CardTitle>{workflow.name}</CardTitle>
                        <CardDescription>
                          版本: {workflow.version} | 
                          更新于: {new Date(workflow.updatedAt).toLocaleDateString()}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                          {workflow.description || '无描述'}
                        </p>
                        <div className="flex justify-between items-center">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setSelectedWorkflow(workflow.id)}
                          >
                            查看执行历史
                          </Button>
                          <Button 
                            size="sm"
                            onClick={async () => {
                              try {
                                const response = await fetch(`/api/client-workflows/${workflow.id}/execute`, {
                                  method: 'POST'
                                })
                                
                                if (!response.ok) throw new Error('执行工作流失败')
                                
                                const data = await response.json()
                                if (data.success) {
                                  toast({
                                    title: '成功',
                                    description: '工作流执行已启动'
                                  })
                                  
                                  // 选择当前工作流并切换到仪表板标签
                                  setSelectedWorkflow(workflow.id);
                                  (document.querySelector('[data-value="dashboard"]') as HTMLElement)?.click()
                                } else {
                                  throw new Error(data.error || '执行工作流失败')
                                }
                              } catch (error) {
                                console.error('执行工作流失败:', error)
                                toast({
                                  title: '错误',
                                  description: '执行工作流失败',
                                  variant: 'destructive'
                                })
                              }
                            }}
                          >
                            <Play className="h-4 w-4 mr-2" />
                            执行
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
              
              <TabsContent value="dashboard" className="mt-4">
                <WorkflowDashboard workflowId={selectedWorkflow || undefined} />
              </TabsContent>
            </Tabs>
          </>
        )}
      </div>
    </div>
  )
}
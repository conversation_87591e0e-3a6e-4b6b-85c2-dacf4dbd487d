import { NextRequest, NextResponse } from 'next/server';

// 模拟智能体数据
const mockAgents = [
  {
    id: '1',
    name: '数据分析师',
    role: 'analyst',
    status: 'active',
    capabilities: ['数据分析', '报告生成', '可视化'],
    performance: {
      tasksCompleted: 156,
      successRate: 0.94,
      avgResponseTime: 2.3
    },
    lastActivity: '2024-01-15T10:30:00Z'
  },
  {
    id: '2', 
    name: '代码审查员',
    role: 'reviewer',
    status: 'active',
    capabilities: ['代码审查', '质量检测', '安全分析'],
    performance: {
      tasksCompleted: 89,
      successRate: 0.97,
      avgResponseTime: 1.8
    },
    lastActivity: '2024-01-15T09:45:00Z'
  },
  {
    id: '3',
    name: '文档生成器',
    role: 'writer',
    status: 'busy',
    capabilities: ['文档编写', '内容生成', '格式化'],
    performance: {
      tasksCompleted: 234,
      successRate: 0.91,
      avgResponseTime: 3.1
    },
    lastActivity: '2024-01-15T11:15:00Z'
  }
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('page_size') || '10');
    const type = searchParams.get('type');
    const status = searchParams.get('status');

    console.log(`[API] 获取智能体列表 - 页码: ${page}, 页大小: ${pageSize}`);

    // 过滤数据
    let filteredAgents = mockAgents;
    
    if (type) {
      filteredAgents = filteredAgents.filter(agent => agent.role === type);
    }
    
    if (status) {
      filteredAgents = filteredAgents.filter(agent => agent.status === status);
    }

    // 分页
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedAgents = filteredAgents.slice(startIndex, endIndex);

    const response = {
      success: true,
      data: {
        agents: paginatedAgents,
        total: filteredAgents.length,
        page,
        page_size: pageSize,
        total_pages: Math.ceil(filteredAgents.length / pageSize)
      }
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('[API错误] 获取智能体列表失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '获取智能体列表失败',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    console.log('[API] 创建智能体:', data);

    // 模拟创建智能体
    const newAgent = {
      id: String(mockAgents.length + 1),
      ...data,
      status: 'active',
      performance: {
        tasksCompleted: 0,
        successRate: 1.0,
        avgResponseTime: 0
      },
      lastActivity: new Date().toISOString()
    };

    mockAgents.push(newAgent);

    return NextResponse.json({
      success: true,
      data: newAgent
    });
  } catch (error) {
    console.error('[API错误] 创建智能体失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '创建智能体失败',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
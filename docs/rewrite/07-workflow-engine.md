# SGASpace 工作流引擎设计

## 🔄 工作流引擎概览

SGASpace 工作流引擎是一个**可视化、事件驱动**的工作流执行系统，支持复杂的业务流程自动化。通过拖拽式设计器，用户可以轻松创建包含多个智能体协作的工作流。

### 核心特性

1. **可视化设计** - 基于React Flow的拖拽式工作流编辑器
2. **智能体集成** - 无缝集成各类AI智能体
3. **流程控制** - 支持条件分支、循环、并行执行
4. **实时监控** - 工作流执行状态实时可视化
5. **错误处理** - 完善的异常处理和重试机制
6. **版本管理** - 工作流版本控制和回滚

## 🏗️ 工作流架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "设计层"
        A[工作流设计器]
        B[节点库]
        C[模板库]
    end
    
    subgraph "执行层"
        D[工作流引擎]
        E[任务调度器]
        F[状态管理器]
    end
    
    subgraph "节点类型"
        G[智能体节点]
        H[条件节点]
        I[循环节点]
        J[工具节点]
        K[数据节点]
    end
    
    subgraph "存储层"
        L[工作流定义]
        M[执行历史]
        N[状态快照]
    end
    
    A --> D
    B --> G
    C --> H
    D --> E
    E --> F
    G --> L
    H --> M
    I --> N
```

### 工作流定义结构

```typescript
// types/workflow.ts
export interface WorkflowDefinition {
  id: string
  name: string
  description?: string
  version: string
  nodes: WorkflowNode[]
  edges: WorkflowEdge[]
  variables: WorkflowVariable[]
  settings: WorkflowSettings
}

export interface WorkflowNode {
  id: string
  type: NodeType
  position: { x: number; y: number }
  data: NodeData
  config: NodeConfig
}

export interface WorkflowEdge {
  id: string
  source: string
  target: string
  sourceHandle?: string
  targetHandle?: string
  condition?: EdgeCondition
}

export enum NodeType {
  START = 'start',
  END = 'end',
  AGENT = 'agent',
  CONDITION = 'condition',
  LOOP = 'loop',
  PARALLEL = 'parallel',
  TOOL = 'tool',
  DATA = 'data',
  DELAY = 'delay'
}
```

## 🎨 可视化工作流设计器

### React Flow 集成

```typescript
// components/workflow/workflow-designer.tsx
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  useReactFlow
} from 'reactflow'

export function WorkflowDesigner({ workflowId }: { workflowId: string }) {
  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])
  const { project } = useReactFlow()
  
  // 自定义节点类型
  const nodeTypes = {
    agent: AgentNode,
    condition: ConditionNode,
    loop: LoopNode,
    parallel: ParallelNode,
    tool: ToolNode,
    data: DataNode
  }
  
  const onConnect = useCallback(
    (params) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )
  
  const onDrop = useCallback(
    (event) => {
      event.preventDefault()
      
      const reactFlowBounds = event.currentTarget.getBoundingClientRect()
      const type = event.dataTransfer.getData('application/reactflow')
      const position = project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      })
      
      const newNode = {
        id: generateId(),
        type,
        position,
        data: { label: `${type} node` },
      }
      
      setNodes((nds) => nds.concat(newNode))
    },
    [project, setNodes]
  )
  
  return (
    <div className="h-full w-full">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onDrop={onDrop}
        onDragOver={(event) => event.preventDefault()}
        nodeTypes={nodeTypes}
        fitView
      >
        <Background />
        <Controls />
        <MiniMap />
      </ReactFlow>
    </div>
  )
}
```

### 智能体节点组件

```typescript
// components/workflow/nodes/agent-node.tsx
export function AgentNode({ data, selected }: NodeProps) {
  const [agent, setAgent] = useState<Agent | null>(null)
  const [isConfigOpen, setIsConfigOpen] = useState(false)
  
  return (
    <div className={cn(
      "bg-white border-2 rounded-lg shadow-sm min-w-[200px]",
      selected ? "border-blue-500" : "border-gray-200"
    )}>
      <Handle type="target" position={Position.Top} />
      
      <div className="p-4">
        <div className="flex items-center gap-2 mb-2">
          <Bot className="w-4 h-4 text-blue-500" />
          <span className="font-medium">智能体节点</span>
        </div>
        
        {agent ? (
          <div className="space-y-2">
            <div className="text-sm font-medium">{agent.name}</div>
            <div className="text-xs text-gray-500">{agent.type}</div>
            <div className="flex items-center gap-1">
              <div className={cn(
                "w-2 h-2 rounded-full",
                agent.status === 'ACTIVE' ? "bg-green-500" : "bg-gray-400"
              )} />
              <span className="text-xs">{agent.status}</span>
            </div>
          </div>
        ) : (
          <div className="text-sm text-gray-500">
            点击配置智能体
          </div>
        )}
        
        <Button
          variant="outline"
          size="sm"
          className="w-full mt-2"
          onClick={() => setIsConfigOpen(true)}
        >
          配置
        </Button>
      </div>
      
      <Handle type="source" position={Position.Bottom} />
      
      <AgentConfigDialog
        open={isConfigOpen}
        onOpenChange={setIsConfigOpen}
        onAgentSelect={setAgent}
      />
    </div>
  )
}
```

## ⚙️ 工作流执行引擎

### 执行引擎核心

```typescript
// server/workflow/workflow-engine.ts
export class WorkflowEngine {
  private executionQueue: Queue
  private stateManager: WorkflowStateManager
  private agentFactory: AgentFactory
  
  constructor() {
    this.executionQueue = new Queue('workflow-execution')
    this.stateManager = new WorkflowStateManager()
    this.agentFactory = new AgentFactory()
  }
  
  async executeWorkflow(workflowId: string, input?: any): Promise<WorkflowExecution> {
    // 1. 加载工作流定义
    const workflow = await this.loadWorkflow(workflowId)
    
    // 2. 创建执行实例
    const execution = await this.createExecution(workflow, input)
    
    // 3. 初始化执行状态
    await this.stateManager.initializeExecution(execution)
    
    // 4. 开始执行
    await this.startExecution(execution)
    
    return execution
  }
  
  private async startExecution(execution: WorkflowExecution): Promise<void> {
    const startNodes = this.findStartNodes(execution.workflow)
    
    for (const node of startNodes) {
      await this.executeNode(execution, node)
    }
  }
  
  private async executeNode(execution: WorkflowExecution, node: WorkflowNode): Promise<void> {
    try {
      // 更新节点状态
      await this.stateManager.updateNodeStatus(execution.id, node.id, 'RUNNING')
      
      // 执行节点逻辑
      const result = await this.processNode(execution, node)
      
      // 更新节点状态和结果
      await this.stateManager.updateNodeStatus(execution.id, node.id, 'COMPLETED', result)
      
      // 执行后续节点
      await this.executeNextNodes(execution, node)
      
    } catch (error) {
      // 错误处理
      await this.handleNodeError(execution, node, error)
    }
  }
  
  private async processNode(execution: WorkflowExecution, node: WorkflowNode): Promise<any> {
    switch (node.type) {
      case NodeType.AGENT:
        return await this.executeAgentNode(execution, node)
      case NodeType.CONDITION:
        return await this.executeConditionNode(execution, node)
      case NodeType.LOOP:
        return await this.executeLoopNode(execution, node)
      case NodeType.PARALLEL:
        return await this.executeParallelNode(execution, node)
      case NodeType.TOOL:
        return await this.executeToolNode(execution, node)
      default:
        throw new Error(`Unsupported node type: ${node.type}`)
    }
  }
  
  private async executeAgentNode(execution: WorkflowExecution, node: WorkflowNode): Promise<any> {
    const agentConfig = node.data.agentConfig
    const agent = await this.agentFactory.createAgent(agentConfig)
    
    // 准备输入数据
    const input = await this.prepareNodeInput(execution, node)
    
    // 执行智能体
    const result = await agent.execute(input)
    
    // 记录执行日志
    await this.logAgentExecution(execution.id, node.id, agent.id, input, result)
    
    return result
  }
  
  private async executeConditionNode(execution: WorkflowExecution, node: WorkflowNode): Promise<any> {
    const condition = node.data.condition
    const context = await this.getExecutionContext(execution)
    
    // 评估条件
    const result = await this.evaluateCondition(condition, context)
    
    return { conditionResult: result }
  }
  
  private async executeParallelNode(execution: WorkflowExecution, node: WorkflowNode): Promise<any> {
    const parallelBranches = this.getParallelBranches(execution.workflow, node)
    
    // 并行执行所有分支
    const results = await Promise.all(
      parallelBranches.map(branch => this.executeBranch(execution, branch))
    )
    
    return { parallelResults: results }
  }
}
```

### 状态管理器

```typescript
// server/workflow/state-manager.ts
export class WorkflowStateManager {
  private redis: Redis
  
  constructor() {
    this.redis = new Redis(process.env.REDIS_URL!)
  }
  
  async initializeExecution(execution: WorkflowExecution): Promise<void> {
    const state: ExecutionState = {
      executionId: execution.id,
      status: 'RUNNING',
      startTime: new Date(),
      nodeStates: new Map(),
      variables: new Map(),
      context: {}
    }
    
    await this.saveExecutionState(execution.id, state)
  }
  
  async updateNodeStatus(
    executionId: string,
    nodeId: string,
    status: NodeExecutionStatus,
    result?: any
  ): Promise<void> {
    const state = await this.getExecutionState(executionId)
    
    state.nodeStates.set(nodeId, {
      status,
      result,
      startTime: new Date(),
      endTime: status === 'COMPLETED' || status === 'FAILED' ? new Date() : undefined
    })
    
    await this.saveExecutionState(executionId, state)
    
    // 广播状态更新
    await this.broadcastStateUpdate(executionId, nodeId, status)
  }
  
  async getExecutionState(executionId: string): Promise<ExecutionState> {
    const stateJson = await this.redis.get(`execution:${executionId}`)
    if (!stateJson) {
      throw new Error(`Execution state not found: ${executionId}`)
    }
    
    return JSON.parse(stateJson)
  }
  
  private async saveExecutionState(executionId: string, state: ExecutionState): Promise<void> {
    await this.redis.setex(
      `execution:${executionId}`,
      3600, // 1小时过期
      JSON.stringify(state)
    )
  }
  
  private async broadcastStateUpdate(
    executionId: string,
    nodeId: string,
    status: NodeExecutionStatus
  ): Promise<void> {
    const message = {
      type: 'workflow:node:status',
      executionId,
      nodeId,
      status,
      timestamp: new Date()
    }
    
    await this.redis.publish('workflow-updates', JSON.stringify(message))
  }
}
```

## 🔧 节点类型实现

### 条件节点

```typescript
// server/workflow/nodes/condition-node.ts
export class ConditionNodeExecutor {
  async execute(context: ExecutionContext, config: ConditionConfig): Promise<boolean> {
    const { condition, operator, value } = config
    const actualValue = this.resolveValue(condition, context)
    
    switch (operator) {
      case 'equals':
        return actualValue === value
      case 'not_equals':
        return actualValue !== value
      case 'greater_than':
        return Number(actualValue) > Number(value)
      case 'less_than':
        return Number(actualValue) < Number(value)
      case 'contains':
        return String(actualValue).includes(String(value))
      case 'regex':
        return new RegExp(String(value)).test(String(actualValue))
      default:
        throw new Error(`Unsupported operator: ${operator}`)
    }
  }
  
  private resolveValue(expression: string, context: ExecutionContext): any {
    // 支持变量引用，如 ${variable_name}
    return expression.replace(/\$\{([^}]+)\}/g, (match, varName) => {
      return context.variables.get(varName) || match
    })
  }
}
```

### 循环节点

```typescript
// server/workflow/nodes/loop-node.ts
export class LoopNodeExecutor {
  async execute(
    context: ExecutionContext,
    config: LoopConfig,
    childNodes: WorkflowNode[]
  ): Promise<any[]> {
    const results: any[] = []
    const { type, condition, maxIterations = 100 } = config
    
    switch (type) {
      case 'for_each':
        return await this.executeForEach(context, config, childNodes)
      case 'while':
        return await this.executeWhile(context, config, childNodes)
      case 'for':
        return await this.executeFor(context, config, childNodes)
      default:
        throw new Error(`Unsupported loop type: ${type}`)
    }
  }
  
  private async executeForEach(
    context: ExecutionContext,
    config: LoopConfig,
    childNodes: WorkflowNode[]
  ): Promise<any[]> {
    const items = context.variables.get(config.iterableVariable) || []
    const results: any[] = []
    
    for (const [index, item] of items.entries()) {
      // 创建循环上下文
      const loopContext = {
        ...context,
        variables: new Map([
          ...context.variables,
          [config.itemVariable || 'item', item],
          [config.indexVariable || 'index', index]
        ])
      }
      
      // 执行子节点
      const result = await this.executeChildNodes(loopContext, childNodes)
      results.push(result)
    }
    
    return results
  }
  
  private async executeWhile(
    context: ExecutionContext,
    config: LoopConfig,
    childNodes: WorkflowNode[]
  ): Promise<any[]> {
    const results: any[] = []
    let iteration = 0
    
    while (iteration < config.maxIterations!) {
      // 评估循环条件
      const shouldContinue = await this.evaluateCondition(config.condition!, context)
      if (!shouldContinue) break
      
      // 执行子节点
      const result = await this.executeChildNodes(context, childNodes)
      results.push(result)
      
      iteration++
    }
    
    return results
  }
}
```

## 📊 工作流监控与分析

### 实时监控

```typescript
// server/workflow/monitoring.ts
export class WorkflowMonitoringService {
  private eventBus: EventEmitter
  private metricsCollector: MetricsCollector
  
  constructor() {
    this.eventBus = new EventEmitter()
    this.metricsCollector = new MetricsCollector()
    this.setupEventHandlers()
  }
  
  private setupEventHandlers(): void {
    this.eventBus.on('workflow:started', this.handleWorkflowStarted.bind(this))
    this.eventBus.on('workflow:completed', this.handleWorkflowCompleted.bind(this))
    this.eventBus.on('workflow:failed', this.handleWorkflowFailed.bind(this))
    this.eventBus.on('node:executed', this.handleNodeExecuted.bind(this))
  }
  
  private async handleWorkflowStarted(event: WorkflowEvent): Promise<void> {
    await this.metricsCollector.recordWorkflowStart(event.executionId)
    
    // 发送实时通知
    await this.broadcastUpdate({
      type: 'workflow_started',
      executionId: event.executionId,
      timestamp: new Date()
    })
  }
  
  private async handleNodeExecuted(event: NodeEvent): Promise<void> {
    await this.metricsCollector.recordNodeExecution({
      executionId: event.executionId,
      nodeId: event.nodeId,
      duration: event.duration,
      success: event.success
    })
    
    // 检查性能阈值
    if (event.duration > 30000) { // 30秒
      await this.alertSlowNode(event)
    }
  }
  
  async getWorkflowMetrics(workflowId: string, timeRange: TimeRange): Promise<WorkflowMetrics> {
    return await this.metricsCollector.getWorkflowMetrics(workflowId, timeRange)
  }
  
  async getExecutionTrace(executionId: string): Promise<ExecutionTrace> {
    const execution = await this.db.workflowExecution.findUnique({
      where: { id: executionId },
      include: {
        agentExecutions: {
          include: { logs: true }
        }
      }
    })
    
    return this.buildExecutionTrace(execution)
  }
}
```

---

**文档版本**: 1.0.0  
**最后更新**: 2024-12-19  
**工作流引擎版本**: v1.0

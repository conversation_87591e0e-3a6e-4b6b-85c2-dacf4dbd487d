#!/bin/bash

# SGASpace 项目初始化脚本
# 基于重写设计文档快速搭建完整项目

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="sgaspace"
NODE_VERSION="18"
PYTHON_VERSION="3.9"
POSTGRES_VERSION="15"
REDIS_VERSION="7"

echo -e "${BLUE}🚀 SGASpace 项目初始化开始...${NC}"
echo -e "${PURPLE}基于重写设计文档构建现代化智能工作台${NC}"

# 检查必要工具
check_requirements() {
    echo -e "${YELLOW}📋 检查系统要求...${NC}"
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装，请先安装 Node.js ${NODE_VERSION}+${NC}"
        exit 1
    fi
    
    # 检查 pnpm
    if ! command -v pnpm &> /dev/null; then
        echo -e "${YELLOW}⚠️  pnpm 未安装，正在安装...${NC}"
        npm install -g pnpm
    fi
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker 未安装，请先安装 Docker${NC}"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose 未安装，请先安装 Docker Compose${NC}"
        exit 1
    fi

    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python 3 未安装，请先安装 Python ${PYTHON_VERSION}+${NC}"
        exit 1
    fi

    # 检查 pip
    if ! command -v pip3 &> /dev/null; then
        echo -e "${RED}❌ pip3 未安装，请先安装 pip3${NC}"
        exit 1
    fi

    echo -e "${GREEN}✅ 系统要求检查通过${NC}"
}

# 创建项目目录结构
create_project_structure() {
    echo -e "${YELLOW}📁 创建项目目录结构...${NC}"
    
    # 创建主目录
    mkdir -p $PROJECT_NAME
    cd $PROJECT_NAME
    
    # 创建完整目录结构
    mkdir -p {app/{api/{auth,trpc,upload,health},\(auth\)/{login,register},\(dashboard\)/{agents,workflows,knowledge,files,settings}},components/{ui,forms,layout,features/{agents,workflows,knowledge,files},providers},lib,server/{api/routers,agents,workflow,services,utils},prisma/{migrations},public/{images,icons},styles,hooks,stores,types,config,scripts,tests/{__mocks__,components,api,utils,e2e},docs/rewrite}
    
    echo -e "${GREEN}✅ 项目目录结构创建完成${NC}"
}

# 初始化 Next.js 项目
init_nextjs() {
    echo -e "${YELLOW}⚡ 初始化 Next.js 项目...${NC}"
    
    # 创建 package.json
    cat > package.json << 'EOF'
{
  "name": "sgaspace",
  "version": "1.0.0",
  "private": true,
  "description": "企业智能工作台 - 智能体协作平台",
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "db:generate": "prisma generate",
    "db:push": "prisma db push",
    "db:migrate": "prisma migrate dev",
    "db:seed": "tsx prisma/seed.ts",
    "db:reset": "prisma migrate reset",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:e2e": "playwright test",
    "docker:dev": "docker-compose up -d",
    "docker:prod": "docker-compose -f docker-compose.prod.yml up -d"
  },
  "dependencies": {
    "next": "^15.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "typescript": "^5.0.0",
    "@prisma/client": "^5.0.0",
    "@trpc/server": "^10.0.0",
    "@trpc/client": "^10.0.0",
    "@trpc/react-query": "^10.0.0",
    "@trpc/next": "^10.0.0",
    "@tanstack/react-query": "^5.0.0",
    "next-auth": "^4.0.0",
    "zod": "^3.0.0",
    "zustand": "^4.0.0",
    "redis": "^4.0.0",
    "bcryptjs": "^2.4.3",
    "@radix-ui/react-slot": "^1.0.0",
    "@radix-ui/react-dialog": "^1.0.0",
    "@radix-ui/react-dropdown-menu": "^2.0.0",
    "@radix-ui/react-toast": "^1.0.0",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.0.0",
    "lucide-react": "^0.300.0",
    "reactflow": "^11.0.0",
    "socket.io": "^4.0.0",
    "socket.io-client": "^4.0.0",
    "qdrant-js": "^1.0.0",
    "minio": "^7.0.0",
    "bull": "^4.0.0",
    "winston": "^3.0.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "@types/bcryptjs": "^2.4.0",
    "prisma": "^5.0.0",
    "eslint": "^8.0.0",
    "eslint-config-next": "^15.0.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "prettier": "^3.0.0",
    "tailwindcss": "^3.0.0",
    "autoprefixer": "^10.0.0",
    "postcss": "^8.0.0",
    "vitest": "^1.0.0",
    "@testing-library/react": "^14.0.0",
    "@testing-library/jest-dom": "^6.0.0",
    "@playwright/test": "^1.0.0",
    "tsx": "^4.0.0",
    "tailwindcss-animate": "^1.0.0"
  }
}
EOF
    
    echo -e "${GREEN}✅ package.json 创建完成${NC}"
}

# 创建配置文件
create_config_files() {
    echo -e "${YELLOW}⚙️  创建配置文件...${NC}"
    
    # Next.js 配置
    cat > next.config.js << 'EOF'
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
    serverComponentsExternalPackages: ['@prisma/client', 'qdrant-js'],
  },
  images: {
    domains: ['localhost'],
  },
  webpack: (config) => {
    config.externals.push({
      'utf-8-validate': 'commonjs utf-8-validate',
      'bufferutil': 'commonjs bufferutil',
    })
    return config
  },
}

module.exports = nextConfig
EOF
    
    # TypeScript 配置
    cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [{ "name": "next" }],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@/components/*": ["components/*"],
      "@/lib/*": ["lib/*"],
      "@/types/*": ["types/*"],
      "@/server/*": ["server/*"],
      "@/hooks/*": ["hooks/*"],
      "@/stores/*": ["stores/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
EOF
    
    # Tailwind CSS 配置
    cat > tailwind.config.js << 'EOF'
/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
EOF
    
    # Prisma Schema
    cat > prisma/schema.prisma << 'EOF'
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Tenant {
  id        String      @id @default(cuid())
  name      String
  slug      String      @unique
  settings  Json        @default("{}")
  status    TenantStatus @default(ACTIVE)
  createdAt DateTime    @default(now()) @map("created_at")
  updatedAt DateTime    @updatedAt @map("updated_at")

  users         User[]
  agents        Agent[]
  workflows     Workflow[]
  knowledgeBases KnowledgeBase[]
  documents     Document[]

  @@map("tenants")
}

model User {
  id           String    @id @default(cuid())
  tenantId     String    @map("tenant_id")
  email        String    @unique
  name         String?
  passwordHash String    @map("password_hash")
  role         UserRole  @default(USER)
  preferences  Json      @default("{}")
  lastLoginAt  DateTime? @map("last_login_at")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  tenant    Tenant     @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  agents    Agent[]
  workflows Workflow[]
  documents Document[]

  @@map("users")
  @@index([tenantId, email])
}

model Agent {
  id           String       @id @default(cuid())
  tenantId     String       @map("tenant_id")
  createdById  String       @map("created_by_id")
  name         String
  description  String?
  type         AgentType
  config       Json         @default("{}")
  status       AgentStatus  @default(INACTIVE)
  capabilities Json         @default("[]")
  createdAt    DateTime     @default(now()) @map("created_at")
  updatedAt    DateTime     @updatedAt @map("updated_at")

  tenant          Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy       User             @relation(fields: [createdById], references: [id])
  workflowAgents  WorkflowAgent[]
  executions      AgentExecution[]

  @@map("agents")
  @@index([tenantId, type])
}

enum TenantStatus {
  ACTIVE
  SUSPENDED
  DELETED
}

enum UserRole {
  ADMIN
  MANAGER
  USER
  VIEWER
}

enum AgentType {
  TASK_PLANNING
  KNOWLEDGE_MANAGEMENT
  DECISION_SUPPORT
  TOOL_INTEGRATION
  COLLABORATION
  CUSTOM
}

enum AgentStatus {
  ACTIVE
  INACTIVE
  ERROR
  MAINTENANCE
}
EOF
    
    # 环境变量示例
    cat > .env.example << 'EOF'
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/sgaspace"

# Redis
REDIS_URL="redis://localhost:6379"

# Qdrant Vector Database
QDRANT_URL="http://localhost:6333"
QDRANT_API_KEY=""

# MinIO Object Storage
MINIO_ENDPOINT="localhost"
MINIO_PORT="9000"
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin"
MINIO_USE_SSL="false"

# NextAuth
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"

# Encryption
ENCRYPTION_KEY="your-32-character-encryption-key-here"

# OpenAI API (for LLM integration)
OPENAI_API_KEY="your-openai-api-key"

# App Configuration
NODE_ENV="development"
LOG_LEVEL="info"
EOF
    
    echo -e "${GREEN}✅ 配置文件创建完成${NC}"
}

# 创建 Docker 配置
create_docker_config() {
    echo -e "${YELLOW}🐳 创建 Docker 配置...${NC}"
    
    # Docker Compose 开发环境
    cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: sgaspace-postgres
    environment:
      POSTGRES_DB: sgaspace
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U user -d sgaspace"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: sgaspace-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  qdrant:
    image: qdrant/qdrant:latest
    container_name: sgaspace-qdrant
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  minio:
    image: minio/minio:latest
    container_name: sgaspace-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
  redis_data:
  qdrant_data:
  minio_data:
EOF
    
    echo -e "${GREEN}✅ Docker 配置创建完成${NC}"
}

# 安装依赖
install_dependencies() {
    echo -e "${YELLOW}📦 安装项目依赖...${NC}"

    # 安装Node.js依赖
    pnpm install

    # 安装Python依赖和CAMEL框架
    echo -e "${YELLOW}🐪 安装CAMEL框架...${NC}"

    # 创建Python虚拟环境
    python3 -m venv venv
    source venv/bin/activate

    # 安装CAMEL框架
    pip install camel-ai[all]

    # 创建requirements.txt
    cat > requirements.txt << 'EOF'
camel-ai[all]>=0.1.5
openai>=1.0.0
redis>=4.0.0
qdrant-client>=1.0.0
fastapi>=0.100.0
uvicorn>=0.20.0
python-multipart>=0.0.6
python-dotenv>=1.0.0
pydantic>=2.0.0
EOF

    pip install -r requirements.txt

    echo -e "${GREEN}✅ 依赖安装完成${NC}"
}

# 启动开发环境
start_dev_environment() {
    echo -e "${YELLOW}🚀 启动开发环境...${NC}"
    
    # 启动数据库服务
    docker-compose up -d
    
    # 等待服务启动
    echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
    sleep 30
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        echo -e "${GREEN}✅ 开发环境启动完成${NC}"
    else
        echo -e "${RED}❌ 服务启动失败，请检查 Docker 日志${NC}"
        docker-compose logs
        exit 1
    fi
}

# 初始化数据库
init_database() {
    echo -e "${YELLOW}🗄️  初始化数据库...${NC}"
    
    # 生成 Prisma 客户端
    pnpm db:generate
    
    # 推送数据库模式
    pnpm db:push
    
    echo -e "${GREEN}✅ 数据库初始化完成${NC}"
}

# 主函数
main() {
    echo -e "${BLUE}🎯 开始初始化 SGASpace 项目${NC}"
    echo -e "${PURPLE}企业智能工作台 - 智能体协作平台${NC}"
    echo ""
    
    check_requirements
    create_project_structure
    init_nextjs
    create_config_files
    create_docker_config
    install_dependencies
    start_dev_environment
    init_database
    
    echo ""
    echo -e "${GREEN}🎉 项目初始化完成！${NC}"
    echo -e "${BLUE}📋 下一步操作：${NC}"
    echo -e "  1. cd $PROJECT_NAME"
    echo -e "  2. 复制 .env.example 到 .env.local 并配置环境变量"
    echo -e "  3. 运行 pnpm dev 启动开发服务器"
    echo -e "  4. 访问 http://localhost:3000"
    echo ""
    echo -e "${YELLOW}📚 查看文档：docs/rewrite/README.md${NC}"
    echo -e "${YELLOW}🔧 开发指南：docs/rewrite/11-development-guide.md${NC}"
    echo ""
    echo -e "${PURPLE}🚀 开始构建您的智能工作台吧！${NC}"
}

# 执行主函数
main "$@"
EOF

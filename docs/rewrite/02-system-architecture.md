# SGASpace 系统架构设计

## 🏗️ 架构概览

SGASpace 采用现代化的**单体优先**架构，基于 Next.js 15 全栈框架构建，实现前后端一体化开发。通过合理的模块化设计，在保持简单性的同时支持未来的扩展需求。

### 设计原则

1. **简化优先** - 避免过度工程化，专注核心业务价值
2. **类型安全** - 端到端 TypeScript，减少运行时错误
3. **开发体验** - 统一技术栈，热重载，自动类型推导
4. **渐进增强** - 从 MVP 开始，支持模块化扩展
5. **云原生** - 容器化部署，支持水平扩展

## 🎯 整体架构图

```mermaid
graph TB
    subgraph "客户端层"
        A[Web 浏览器]
        B[桌面应用 Electron]
        C[移动端 PWA]
    end
    
    subgraph "应用层 - Next.js 15"
        D[App Router]
        E[React Server Components]
        F[API Routes + tRPC]
        G[WebSocket Server]
        H[中间件层]
    end
    
    subgraph "业务逻辑层"
        I[智能体管理服务]
        J[工作流引擎]
        K[知识库服务]
        L[多租户服务]
        M[实时协作服务]
    end
    
    subgraph "数据访问层"
        N[Prisma ORM]
        O[Redis 客户端]
        P[向量数据库客户端]
        Q[文件存储客户端]
    end
    
    subgraph "数据存储层"
        R[PostgreSQL 15]
        S[Redis 7]
        T[Qdrant 向量数据库]
        U[MinIO 对象存储]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    I --> J
    J --> K
    K --> L
    L --> M
    M --> N
    N --> O
    O --> P
    P --> Q
    Q --> R
    R --> S
    S --> T
    T --> U
```

## 🔧 技术栈详解

### 前端技术栈

#### Next.js 15 App Router
```typescript
// app/layout.tsx - 根布局
export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          <div className="min-h-screen bg-background">
            <Header />
            <div className="flex">
              <Sidebar />
              <main className="flex-1">
                {children}
              </main>
            </div>
          </div>
        </Providers>
      </body>
    </html>
  )
}
```

**选择理由**:
- 内置 SSR/SSG 支持，优化首屏加载
- 文件系统路由，简化路由管理
- React Server Components，减少客户端 JS
- 自动代码分割和优化

#### UI 组件库 - shadcn/ui
```typescript
// components/ui/button.tsx
import { cn } from "@/lib/utils"
import { Slot } from "@radix-ui/react-slot"

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = "default", size = "default", asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
```

**技术选型**:
- **shadcn/ui**: 可定制的现代组件库
- **Radix UI**: 无障碍访问支持
- **Tailwind CSS**: 原子化 CSS，快速开发
- **React Flow**: 工作流可视化编辑器

### 后端技术栈

#### API 设计 - tRPC
```typescript
// server/api/routers/agent.ts
export const agentRouter = createTRPCRouter({
  list: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      type: z.enum(['task_planning', 'knowledge_management', 'decision_support']).optional(),
    }))
    .query(async ({ input, ctx }) => {
      const { page, limit, type } = input
      const skip = (page - 1) * limit
      
      const where = {
        tenantId: ctx.session.user.tenantId,
        ...(type && { type }),
      }
      
      const [agents, total] = await Promise.all([
        ctx.db.agent.findMany({
          where,
          skip,
          take: limit,
          include: {
            _count: {
              select: { workflows: true }
            }
          },
          orderBy: { createdAt: 'desc' }
        }),
        ctx.db.agent.count({ where })
      ])
      
      return {
        agents,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    }),

  create: protectedProcedure
    .input(createAgentSchema)
    .mutation(async ({ input, ctx }) => {
      return await ctx.db.agent.create({
        data: {
          ...input,
          tenantId: ctx.session.user.tenantId,
          createdById: ctx.session.user.id,
        },
        include: {
          createdBy: {
            select: { id: true, name: true, email: true }
          }
        }
      })
    }),
})
```

**优势**:
- 端到端类型安全
- 自动生成客户端代码
- 内置输入验证和错误处理
- 优秀的开发体验

#### 数据库设计 - Prisma + PostgreSQL
```prisma
// prisma/schema.prisma
model Agent {
  id          String      @id @default(cuid())
  name        String
  description String?
  type        AgentType
  config      Json        @default("{}")
  status      AgentStatus @default(INACTIVE)
  
  // 多租户
  tenantId    String
  tenant      Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  // 创建者
  createdById String
  createdBy   User        @relation(fields: [createdById], references: [id])
  
  // 关联关系
  workflows   WorkflowAgent[]
  executions  AgentExecution[]
  
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  @@map("agents")
  @@index([tenantId, type])
  @@index([tenantId, status])
}

model Workflow {
  id          String         @id @default(cuid())
  name        String
  description String?
  definition  Json           @default("{}")
  status      WorkflowStatus @default(DRAFT)
  
  // 多租户
  tenantId    String
  tenant      Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  // 创建者
  createdById String
  createdBy   User           @relation(fields: [createdById], references: [id])
  
  // 关联关系
  agents      WorkflowAgent[]
  executions  WorkflowExecution[]
  
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  @@map("workflows")
  @@index([tenantId, status])
}
```

### 数据存储架构

#### 主数据库 - PostgreSQL 15
```sql
-- 租户隔离策略
CREATE POLICY tenant_isolation_agents ON agents
  FOR ALL TO authenticated_user
  USING (tenant_id = current_setting('app.current_tenant_id'));

-- 性能优化索引
CREATE INDEX CONCURRENTLY idx_agents_tenant_type ON agents(tenant_id, type);
CREATE INDEX CONCURRENTLY idx_workflows_tenant_status ON workflows(tenant_id, status);
CREATE INDEX CONCURRENTLY idx_executions_workflow_created ON workflow_executions(workflow_id, created_at DESC);
```

#### 缓存层 - Redis 7
```typescript
// lib/redis.ts
export class CacheService {
  private redis: Redis
  
  constructor() {
    this.redis = new Redis(process.env.REDIS_URL!)
  }
  
  // 智能体状态缓存
  async cacheAgentStatus(agentId: string, status: AgentStatus, ttl = 300) {
    const key = `agent:status:${agentId}`
    await this.redis.setex(key, ttl, JSON.stringify(status))
  }
  
  // 工作流执行状态缓存
  async cacheWorkflowExecution(executionId: string, state: any, ttl = 600) {
    const key = `workflow:execution:${executionId}`
    await this.redis.setex(key, ttl, JSON.stringify(state))
  }
  
  // 用户会话缓存
  async cacheUserSession(userId: string, session: any, ttl = 3600) {
    const key = `user:session:${userId}`
    await this.redis.setex(key, ttl, JSON.stringify(session))
  }
}
```

#### 向量数据库 - Qdrant
```typescript
// lib/vector-store.ts
export class VectorStoreService {
  private client: QdrantClient
  
  constructor() {
    this.client = new QdrantClient({
      url: process.env.QDRANT_URL!,
      apiKey: process.env.QDRANT_API_KEY,
    })
  }
  
  // 创建租户集合
  async createTenantCollection(tenantId: string) {
    const collectionName = `tenant_${tenantId}_knowledge`
    
    await this.client.createCollection(collectionName, {
      vectors: {
        size: 1536, // OpenAI embedding 维度
        distance: 'Cosine'
      }
    })
  }
  
  // 向量化文档
  async indexDocument(tenantId: string, document: Document) {
    const collectionName = `tenant_${tenantId}_knowledge`
    const chunks = await this.chunkDocument(document)
    
    const points = await Promise.all(
      chunks.map(async (chunk, index) => ({
        id: `${document.id}_${index}`,
        vector: await this.embedText(chunk.content),
        payload: {
          documentId: document.id,
          chunkIndex: index,
          content: chunk.content,
          metadata: chunk.metadata
        }
      }))
    )
    
    await this.client.upsert(collectionName, {
      wait: true,
      points
    })
  }
  
  // 语义检索
  async searchSimilar(tenantId: string, query: string, limit = 10) {
    const collectionName = `tenant_${tenantId}_knowledge`
    const queryVector = await this.embedText(query)
    
    const results = await this.client.search(collectionName, {
      vector: queryVector,
      limit,
      with_payload: true,
      score_threshold: 0.7
    })
    
    return results.map(result => ({
      content: result.payload?.content,
      score: result.score,
      metadata: result.payload?.metadata
    }))
  }
}
```

## 🔐 安全架构

### 认证授权 - NextAuth.js
```typescript
// lib/auth.ts
export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) return null
        
        const user = await verifyUser(credentials.email, credentials.password)
        if (!user) return null
        
        return {
          id: user.id,
          email: user.email,
          name: user.name,
          tenantId: user.tenantId,
          role: user.role
        }
      }
    })
  ],
  session: { strategy: "jwt" },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.tenantId = user.tenantId
        token.role = user.role
      }
      return token
    },
    async session({ session, token }) {
      session.user.id = token.sub!
      session.user.tenantId = token.tenantId as string
      session.user.role = token.role as string
      return session
    }
  }
}
```

### 多租户中间件
```typescript
// middleware.ts
export async function middleware(request: NextRequest) {
  // API 路由租户上下文注入
  if (request.nextUrl.pathname.startsWith('/api/')) {
    const session = await getToken({ req: request })
    if (!session?.tenantId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // 注入租户上下文
    request.headers.set('x-tenant-id', session.tenantId)
    request.headers.set('x-user-id', session.sub!)
  }
  
  return NextResponse.next()
}
```

## 📊 实时通信架构

### WebSocket 服务
```typescript
// lib/websocket.ts
export class WebSocketService {
  private io: Server
  
  constructor(server: any) {
    this.io = new Server(server, {
      cors: { origin: "*" },
      transports: ['websocket', 'polling']
    })
    
    this.setupEventHandlers()
  }
  
  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      // 用户加入租户房间
      socket.on('join-tenant', async (tenantId: string) => {
        const session = await this.validateSession(socket)
        if (session?.tenantId === tenantId) {
          socket.join(`tenant:${tenantId}`)
        }
      })
      
      // 工作流状态更新
      socket.on('workflow-update', (data) => {
        socket.to(`tenant:${data.tenantId}`).emit('workflow-status', data)
      })
      
      // 智能体状态更新
      socket.on('agent-update', (data) => {
        socket.to(`tenant:${data.tenantId}`).emit('agent-status', data)
      })
    })
  }
  
  // 广播租户消息
  broadcastToTenant(tenantId: string, event: string, data: any) {
    this.io.to(`tenant:${tenantId}`).emit(event, data)
  }
}
```

---

**文档版本**: 1.0.0  
**最后更新**: 2024-12-19  
**架构负责人**: 技术团队

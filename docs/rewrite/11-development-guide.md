# SGASpace 开发指南

## 🚀 快速开始

### 环境要求

- **Node.js**: 18.0+ 
- **pnpm**: 8.0+
- **Docker**: 20.0+
- **PostgreSQL**: 15+
- **Redis**: 7+

### 项目初始化

```bash
# 1. 克隆项目
git clone <repository-url>
cd sgaspace

# 2. 安装依赖
pnpm install

# 3. 环境配置
cp .env.example .env.local
# 编辑 .env.local 配置数据库连接等

# 4. 启动数据库服务
docker-compose up -d postgres redis qdrant minio

# 5. 数据库初始化
pnpm db:push
pnpm db:seed

# 6. 启动开发服务器
pnpm dev
```

### 项目结构

```
sgaspace/
├── app/                    # Next.js App Router
│   ├── (auth)/            # 认证页面组
│   ├── (dashboard)/       # 主应用页面组
│   ├── api/               # API 路由
│   └── globals.css        # 全局样式
├── components/            # React 组件
│   ├── ui/               # 基础 UI 组件
│   ├── forms/            # 表单组件
│   ├── layout/           # 布局组件
│   └── features/         # 功能组件
├── lib/                  # 工具库
├── server/               # 服务器端代码
│   ├── api/             # tRPC API
│   ├── agents/          # 智能体框架
│   ├── workflow/        # 工作流引擎
│   └── services/        # 业务服务
├── prisma/              # 数据库相关
├── types/               # TypeScript 类型
└── docs/                # 项目文档
```

## 🔧 开发工作流

### 1. 功能开发流程

```bash
# 1. 创建功能分支
git checkout -b feature/agent-management

# 2. 开发功能
# - 编写代码
# - 添加测试
# - 更新文档

# 3. 运行测试
pnpm test
pnpm type-check
pnpm lint

# 4. 提交代码
git add .
git commit -m "feat: add agent management functionality"

# 5. 推送并创建 PR
git push origin feature/agent-management
```

### 2. 代码规范

#### TypeScript 规范

```typescript
// ✅ 好的实践
interface User {
  id: string
  name: string
  email: string
  createdAt: Date
}

export async function createUser(data: CreateUserInput): Promise<User> {
  const user = await db.user.create({
    data: {
      ...data,
      id: generateId(),
      createdAt: new Date()
    }
  })
  
  return user
}

// ❌ 避免的写法
export async function createUser(data: any): Promise<any> {
  // 缺少类型定义
  const user = await db.user.create({ data })
  return user
}
```

#### React 组件规范

```typescript
// ✅ 好的实践
interface AgentCardProps {
  agent: Agent
  onEdit?: (agent: Agent) => void
  onDelete?: (agentId: string) => void
}

export function AgentCard({ agent, onEdit, onDelete }: AgentCardProps) {
  const handleEdit = useCallback(() => {
    onEdit?.(agent)
  }, [agent, onEdit])
  
  return (
    <Card className="p-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-semibold">{agent.name}</h3>
          <p className="text-sm text-muted-foreground">{agent.description}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={handleEdit}>
            编辑
          </Button>
          <Button 
            variant="destructive" 
            size="sm" 
            onClick={() => onDelete?.(agent.id)}
          >
            删除
          </Button>
        </div>
      </div>
    </Card>
  )
}
```

### 3. API 开发

#### tRPC 路由定义

```typescript
// server/api/routers/agent.ts
export const agentRouter = createTRPCRouter({
  list: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      search: z.string().optional(),
      type: z.nativeEnum(AgentType).optional(),
    }))
    .query(async ({ input, ctx }) => {
      const { page, limit, search, type } = input
      const skip = (page - 1) * limit
      
      const where = {
        tenantId: ctx.session.user.tenantId,
        ...(search && {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } }
          ]
        }),
        ...(type && { type })
      }
      
      const [agents, total] = await Promise.all([
        ctx.db.agent.findMany({
          where,
          skip,
          take: limit,
          include: {
            createdBy: {
              select: { id: true, name: true }
            },
            _count: {
              select: { workflowAgents: true }
            }
          },
          orderBy: { createdAt: 'desc' }
        }),
        ctx.db.agent.count({ where })
      ])
      
      return {
        agents,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    }),

  create: protectedProcedure
    .input(createAgentSchema)
    .mutation(async ({ input, ctx }) => {
      // 验证权限
      if (ctx.session.user.role === 'VIEWER') {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: '没有创建智能体的权限'
        })
      }
      
      const agent = await ctx.db.agent.create({
        data: {
          ...input,
          tenantId: ctx.session.user.tenantId,
          createdById: ctx.session.user.id,
        },
        include: {
          createdBy: {
            select: { id: true, name: true }
          }
        }
      })
      
      // 发送事件通知
      ctx.eventBus.emit('agent:created', {
        agentId: agent.id,
        tenantId: agent.tenantId,
        createdBy: ctx.session.user.id
      })
      
      return agent
    })
})
```

#### 客户端使用

```typescript
// hooks/use-agents.ts
export function useAgents(params?: AgentListParams) {
  return api.agent.list.useQuery(params, {
    keepPreviousData: true,
    staleTime: 5 * 60 * 1000, // 5分钟
  })
}

export function useCreateAgent() {
  const utils = api.useContext()
  
  return api.agent.create.useMutation({
    onSuccess: () => {
      // 刷新列表
      utils.agent.list.invalidate()
      toast.success('智能体创建成功')
    },
    onError: (error) => {
      toast.error(error.message)
    }
  })
}

// 在组件中使用
export function AgentList() {
  const [params, setParams] = useState<AgentListParams>({
    page: 1,
    limit: 20
  })
  
  const { data, isLoading, error } = useAgents(params)
  const createAgent = useCreateAgent()
  
  if (isLoading) return <LoadingSpinner />
  if (error) return <ErrorMessage error={error} />
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between">
        <h1 className="text-2xl font-bold">智能体管理</h1>
        <Button onClick={() => createAgent.mutate(newAgentData)}>
          创建智能体
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {data?.agents.map(agent => (
          <AgentCard key={agent.id} agent={agent} />
        ))}
      </div>
      
      <Pagination
        page={params.page}
        limit={params.limit}
        total={data?.pagination.total || 0}
        onPageChange={(page) => setParams(prev => ({ ...prev, page }))}
      />
    </div>
  )
}
```

## 🧪 测试指南

### 单元测试

```typescript
// __tests__/agents/agent-service.test.ts
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { AgentService } from '@/server/services/agent-service'
import { mockDb } from '@/tests/mocks/db'

describe('AgentService', () => {
  let agentService: AgentService
  
  beforeEach(() => {
    agentService = new AgentService(mockDb)
  })
  
  describe('createAgent', () => {
    it('should create agent successfully', async () => {
      const agentData = {
        name: 'Test Agent',
        type: 'TASK_PLANNING',
        description: 'Test description'
      }
      
      const mockAgent = {
        id: 'agent-1',
        ...agentData,
        tenantId: 'tenant-1',
        createdById: 'user-1',
        status: 'INACTIVE',
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      mockDb.agent.create.mockResolvedValue(mockAgent)
      
      const result = await agentService.createAgent(agentData, {
        tenantId: 'tenant-1',
        userId: 'user-1'
      })
      
      expect(result).toEqual(mockAgent)
      expect(mockDb.agent.create).toHaveBeenCalledWith({
        data: {
          ...agentData,
          tenantId: 'tenant-1',
          createdById: 'user-1'
        }
      })
    })
    
    it('should throw error for invalid agent type', async () => {
      const agentData = {
        name: 'Test Agent',
        type: 'INVALID_TYPE' as any,
        description: 'Test description'
      }
      
      await expect(
        agentService.createAgent(agentData, {
          tenantId: 'tenant-1',
          userId: 'user-1'
        })
      ).rejects.toThrow('Invalid agent type')
    })
  })
})
```

### 集成测试

```typescript
// __tests__/api/agent.test.ts
import { describe, it, expect } from 'vitest'
import { createTRPCMsw } from 'msw-trpc'
import { appRouter } from '@/server/api/root'
import { createTestContext } from '@/tests/helpers/context'

const trpcMsw = createTRPCMsw(appRouter)

describe('/api/trpc/agent', () => {
  it('should list agents', async () => {
    const ctx = await createTestContext({
      user: { id: 'user-1', tenantId: 'tenant-1', role: 'USER' }
    })
    
    const caller = appRouter.createCaller(ctx)
    
    const result = await caller.agent.list({
      page: 1,
      limit: 10
    })
    
    expect(result).toHaveProperty('agents')
    expect(result).toHaveProperty('pagination')
    expect(Array.isArray(result.agents)).toBe(true)
  })
  
  it('should create agent', async () => {
    const ctx = await createTestContext({
      user: { id: 'user-1', tenantId: 'tenant-1', role: 'USER' }
    })
    
    const caller = appRouter.createCaller(ctx)
    
    const agentData = {
      name: 'Test Agent',
      type: 'TASK_PLANNING' as const,
      description: 'Test description',
      config: {}
    }
    
    const result = await caller.agent.create(agentData)
    
    expect(result).toHaveProperty('id')
    expect(result.name).toBe(agentData.name)
    expect(result.type).toBe(agentData.type)
  })
})
```

### E2E 测试

```typescript
// __tests__/e2e/agent-management.test.ts
import { test, expect } from '@playwright/test'

test.describe('Agent Management', () => {
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto('/login')
    await page.fill('[data-testid=email]', '<EMAIL>')
    await page.fill('[data-testid=password]', 'password')
    await page.click('[data-testid=login-button]')
    
    // 等待跳转到仪表板
    await page.waitForURL('/dashboard')
  })
  
  test('should create new agent', async ({ page }) => {
    // 导航到智能体页面
    await page.click('[data-testid=nav-agents]')
    await page.waitForURL('/dashboard/agents')
    
    // 点击创建按钮
    await page.click('[data-testid=create-agent-button]')
    
    // 填写表单
    await page.fill('[data-testid=agent-name]', 'Test Agent')
    await page.selectOption('[data-testid=agent-type]', 'TASK_PLANNING')
    await page.fill('[data-testid=agent-description]', 'Test description')
    
    // 提交表单
    await page.click('[data-testid=submit-button]')
    
    // 验证创建成功
    await expect(page.locator('[data-testid=success-message]')).toBeVisible()
    await expect(page.locator('text=Test Agent')).toBeVisible()
  })
  
  test('should edit existing agent', async ({ page }) => {
    await page.goto('/dashboard/agents')
    
    // 点击编辑按钮
    await page.click('[data-testid=edit-agent-button]')
    
    // 修改名称
    await page.fill('[data-testid=agent-name]', 'Updated Agent Name')
    
    // 保存修改
    await page.click('[data-testid=save-button]')
    
    // 验证修改成功
    await expect(page.locator('text=Updated Agent Name')).toBeVisible()
  })
})
```

## 📦 部署指南

### Docker 部署

```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# 安装依赖
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN npm install -g pnpm && pnpm install --frozen-lockfile

# 构建应用
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# 生成 Prisma 客户端
RUN npx prisma generate

# 构建 Next.js 应用
RUN npm run build

# 生产镜像
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### Docker Compose

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=****************************************/sgaspace
      - REDIS_URL=redis://redis:6379
      - QDRANT_URL=http://qdrant:6333
      - MINIO_ENDPOINT=minio:9000
    depends_on:
      - postgres
      - redis
      - qdrant
      - minio
    restart: unless-stopped

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: sgaspace
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped

  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage
    restart: unless-stopped

  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  qdrant_data:
  minio_data:
```

### 部署脚本

```bash
#!/bin/bash
# scripts/deploy.sh

set -e

echo "🚀 开始部署 SGASpace..."

# 1. 拉取最新代码
git pull origin main

# 2. 构建镜像
docker-compose -f docker-compose.prod.yml build

# 3. 停止旧服务
docker-compose -f docker-compose.prod.yml down

# 4. 启动新服务
docker-compose -f docker-compose.prod.yml up -d

# 5. 运行数据库迁移
docker-compose -f docker-compose.prod.yml exec app npx prisma migrate deploy

# 6. 健康检查
echo "⏳ 等待服务启动..."
sleep 30

if curl -f http://localhost:3000/api/health; then
  echo "✅ 部署成功！"
else
  echo "❌ 部署失败，请检查日志"
  docker-compose -f docker-compose.prod.yml logs app
  exit 1
fi
```

---

**文档版本**: 1.0.0  
**最后更新**: 2024-12-19  
**开发团队**: SGASpace Team

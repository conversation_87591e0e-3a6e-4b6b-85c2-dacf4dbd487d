# SGASpace 重写版 - 企业智能工作台

## 📋 项目概述

SGASpace 重写版是一个现代化的企业智能工作台，专注于**智能体协作**、**工作流自动化**和**知识管理**。基于对原有复杂架构的深度分析，我们采用简化但功能完整的设计，保留所有核心业务价值。

### 🎯 核心价值主张

1. **智能体协作平台** - 支持多个AI智能体协同工作
2. **可视化工作流设计** - 拖拽式工作流编辑器
3. **企业级多租户** - 完整的租户隔离和权限管理
4. **知识库管理** - 文档上传、向量化检索
5. **实时协作** - WebSocket实时状态同步
6. **桌面应用支持** - Electron桌面客户端

## 📁 文档结构

```
docs/rewrite/
├── README.md                           # 本文件 - 项目概览
├── 01-business-requirements.md         # 业务需求分析
├── 02-system-architecture.md          # 系统架构设计
├── 03-database-design.md              # 数据库设计
├── 04-api-specification.md            # API接口规范
├── 05-frontend-architecture.md        # 前端架构设计
├── 06-agent-framework.md              # 智能体框架设计
├── 07-workflow-engine.md              # 工作流引擎设计
├── 08-security-design.md              # 安全架构设计
├── 09-ui-specification.md             # UI设计规范
├── 10-deployment-guide.md             # 部署指南
├── 11-development-guide.md            # 开发指南
└── 12-migration-strategy.md           # 迁移策略
```

## 🏗️ 技术架构概览

### 核心技术栈

```mermaid
graph TB
    subgraph "前端层"
        A[Next.js 15 App Router]
        B[React Server Components]
        C[shadcn/ui + Tailwind CSS]
        D[React Flow - 工作流设计器]
    end
    
    subgraph "API层"
        E[tRPC - 类型安全API]
        F[NextAuth.js - 认证]
        G[WebSocket - 实时通信]
    end
    
    subgraph "业务逻辑层"
        H[智能体管理服务]
        I[工作流引擎]
        J[知识库服务]
        K[多租户服务]
    end
    
    subgraph "数据层"
        L[Prisma ORM]
        M[PostgreSQL - 主数据库]
        N[Redis - 缓存/队列]
        O[向量数据库 - 知识检索]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
    E --> I
    F --> J
    G --> K
    H --> L
    I --> M
    J --> N
    K --> O
```

### 架构设计原则

1. **简化优先** - 避免过度工程化，专注核心功能
2. **类型安全** - 端到端TypeScript，减少运行时错误
3. **开发体验** - 热重载、自动类型推导、统一工具链
4. **渐进增强** - 从MVP开始，逐步添加高级功能
5. **云原生** - 容器化部署，支持水平扩展

## 🎨 用户界面设计

### 主界面布局

```
┌─────────────────────────────────────────────────────────┐
│ 顶部导航栏 - 租户切换 | 用户菜单 | 通知中心              │
├─────────────────────────────────────────────────────────┤
│ 侧边栏     │ 主工作区                                   │
│ ├ 仪表板   │ ┌─────────────────────────────────────────┐ │
│ ├ 智能体   │ │ 工作流设计器                            │ │
│ ├ 工作流   │ │ ┌─────┐    ┌─────┐    ┌─────┐          │ │
│ ├ 知识库   │ │ │智能体│───▶│处理 │───▶│输出 │          │ │
│ ├ 文件     │ │ │ A   │    │节点 │    │节点 │          │ │
│ └ 设置     │ │ └─────┘    └─────┘    └─────┘          │ │
│           │ └─────────────────────────────────────────┘ │
│           │ 智能体状态面板 | 执行日志 | 结果预览        │
└─────────────────────────────────────────────────────────┘
```

### 核心功能模块

#### 1. 智能体管理
- **智能体创建** - 支持多种智能体类型
- **配置管理** - 模型选择、参数调优
- **状态监控** - 实时状态、性能指标
- **协作设置** - 智能体间通信规则

#### 2. 工作流设计器
- **可视化编辑** - 基于React Flow的拖拽界面
- **节点类型** - 智能体节点、条件节点、工具节点
- **流程控制** - 分支、循环、并行执行
- **实时预览** - 工作流执行状态可视化

#### 3. 知识库管理
- **文档上传** - 支持多种格式（PDF、Word、Markdown）
- **向量化处理** - 自动文档分块和向量化
- **语义检索** - 基于向量相似度的智能检索
- **知识图谱** - 文档关系可视化

#### 4. 实时协作
- **状态同步** - WebSocket实时状态更新
- **多用户协作** - 同时编辑工作流
- **通知系统** - 任务完成、错误告警
- **活动日志** - 用户操作记录

## 📊 核心业务流程

### 智能体协作流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant W as 工作流引擎
    participant A1 as 智能体A
    participant A2 as 智能体B
    participant K as 知识库
    
    U->>W: 启动工作流
    W->>A1: 分配任务
    A1->>K: 查询相关知识
    K-->>A1: 返回知识片段
    A1->>A2: 传递处理结果
    A2->>W: 完成任务
    W->>U: 返回最终结果
```

### 多租户数据隔离

```mermaid
graph LR
    subgraph "租户A"
        A1[用户A1]
        A2[用户A2]
        A3[智能体A]
        A4[工作流A]
    end
    
    subgraph "租户B"
        B1[用户B1]
        B2[用户B2]
        B3[智能体B]
        B4[工作流B]
    end
    
    subgraph "数据库"
        D1[租户A数据]
        D2[租户B数据]
    end
    
    A1 --> D1
    A2 --> D1
    A3 --> D1
    A4 --> D1
    
    B1 --> D2
    B2 --> D2
    B3 --> D2
    B4 --> D2
```

## 🚀 开发路线图

### Phase 1: 基础架构 (4周)
- [x] 项目初始化和技术栈搭建
- [ ] 用户认证和多租户基础设施
- [ ] 基础UI组件库
- [ ] 数据库设计和迁移

### Phase 2: 核心功能 (6周)
- [ ] 智能体管理系统
- [ ] 工作流设计器
- [ ] 基础工作流执行引擎
- [ ] 文件上传和管理

### Phase 3: 高级功能 (6周)
- [ ] 知识库和向量检索
- [ ] 实时协作功能
- [ ] 智能体协作框架
- [ ] 高级工作流功能

### Phase 4: 企业功能 (4周)
- [ ] 权限管理和审计
- [ ] 监控和告警
- [ ] 性能优化
- [ ] 桌面应用

## 📈 预期成果

### 技术指标
- **部署时间**: < 10分钟
- **页面加载**: < 2秒
- **API响应**: < 300ms
- **并发用户**: 1000+
- **系统可用性**: 99.9%

### 业务指标
- **用户学习成本**: < 30分钟
- **工作流创建**: < 5分钟
- **智能体响应**: < 10秒
- **知识检索准确率**: > 85%

## 🔗 快速开始

1. **查看业务需求** - [01-business-requirements.md](./01-business-requirements.md)
2. **了解系统架构** - [02-system-architecture.md](./02-system-architecture.md)
3. **数据库设计** - [03-database-design.md](./03-database-design.md)
4. **开始开发** - [11-development-guide.md](./11-development-guide.md)

---

**项目版本**: 1.0.0  
**文档版本**: 2024-12-19  
**维护团队**: SGASpace 开发团队

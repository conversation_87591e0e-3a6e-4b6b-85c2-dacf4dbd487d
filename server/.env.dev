# SGASpace 服务端环境变量示例
#
# 复制此文件为 .env 并填写相应的值

# 应用配置
APP_NAME=SGASpace
DEBUG=True
ENVIRONMENT=development
APP_VERSION=0.1.0

# 大模型API密钥
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_API_BASE=https://api.openai.com/v1
MODEL_DEFAULT=gpt-4-turbo
# ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key
# WENXIN_API_KEY=your-wenxin-api-key
# DEEPSEEK_API_KEY=your-deepseek-api-key

# ==== 大模型API配置 ====
# 基础配置
LLM_DEFAULT_PROVIDER=openai-compatible
LLM_FALLBACK_CHAIN=openai-compatible,siliconflow,openai,mock

# OpenAI Compatible配置
LLM_OPENAI_COMPATIBLE_API_KEY=sk-mqmoyewycswxcvvlygxxeqaasfzwozgjstpotchqerapjldi
LLM_OPENAI_COMPATIBLE_API_BASE=https://api.siliconflow.cn
LLM_OPENAI_COMPATIBLE_TIMEOUT=30
LLM_OPENAI_COMPATIBLE_DEFAULT_MODEL=deepseek-ai/DeepSeek-V2.5

# SiliconFlow配置
SILICONFLOW_API_KEY=sk-mqmoyewycswxcvvlygxxeqaasfzwozgjstpotchqerapjldi
SILICONFLOW_API_BASE=https://api.siliconflow.ai/v1
LLM_SILICONFLOW_API_KEY=sk-mqmoyewycswxcvvlygxxeqaasfzwozgjstpotchqerapjldi
LLM_SILICONFLOW_API_BASE=https://api.siliconflow.ai/v1
LLM_SILICONFLOW_DEFAULT_MODEL=silicon-32b-chat

# ==== 嵌入模型配置 ====
EMBEDDING_DEFAULT_MODEL=silicon-embedding-v1
EMBEDDING_PROVIDER=siliconflow

# ==== 知识库配置 ====
# Milvus向量数据库
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_DEFAULT_COLLECTION=sgaspace_kb

# 知识库文档处理
KNOWLEDGE_CHUNK_SIZE=1000
KNOWLEDGE_CHUNK_OVERLAP=200
KNOWLEDGE_MAX_DOCUMENT_SIZE=10485760  # 10MB
KNOWLEDGE_SUPPORTED_EXTENSIONS=pdf,docx,txt,md,csv

# 文档OCR处理
OCR_ENABLED=false
OCR_USE_GPU=false

# ==== LLM服务配置 ====
# 缓存配置
LLM_CACHE_TYPE=memory
LLM_CACHE_TTL=3600
LLM_CACHE_MAX_SIZE=1000
LLM_CACHE_REDIS_URL=redis://localhost:6380/0

# 数据库配置
DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/sgaspace
TEST_DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/sgaspace_test

# Redis配置
REDIS_URL=redis://localhost:6380/0

# JWT配置
SECRET_KEY=your-secret-key-at-least-32-characters-long
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS配置
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# MinIO配置
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_SECURE=False

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_PATH=./logs

# 身份验证
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256

# 数据库
# PostgreSQL
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_HOST=sgaspace-postgres
POSTGRES_PORT=5432
POSTGRES_DB=sgaspace

# MongoDB
MONGO_HOST=sgaspace-mongo
MONGO_PORT=27017
MONGO_DB=sgaspace

# Redis
REDIS_HOST=sgaspace-redis
REDIS_PORT=6379
REDIS_PASSWORD=

# Milvus
MILVUS_HOST=localhost
MILVUS_PORT=19530

# MinIO
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin
MINIO_HOST=sgaspace-minio
MINIO_PORT=9000
MINIO_USE_SSL=False
MINIO_BUCKET=sgaspace

# 消息队列
PULSAR_HOST=sgaspace-pulsar
PULSAR_PORT=6650

# 日志配置
LOG_PATH=./logs 
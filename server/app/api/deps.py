"""
API依赖项模块
提供FastAPI依赖项，用于路由处理函数中
"""
from typing import Generator, Optional, Union, Any

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from loguru import logger
from pydantic import ValidationError
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.security import decode_access_token
from app.db.session import SessionLocal, get_async_session
from app.models.user import User
from app.schemas.token import TokenPayload
from app.services.tenant_service import verify_tenant_access

# OAuth2密码承载令牌URL
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_PREFIX}/auth/login")

def get_db() -> Generator:
    """数据库会话依赖

    Yields:
        Generator: 数据库会话对象
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_current_user(
    request: Request,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
) -> User:
    """获取当前认证用户

    Args:
        request (Request): 请求对象
        db (Session): 数据库会话
        token (str): 访问令牌

    Raises:
        HTTPException: 认证失败异常

    Returns:
        User: 当前认证用户模型
    """
    try:
        # 解码令牌
        payload = decode_access_token(token)
        token_data = TokenPayload(**payload)
    except (JWTError, ValidationError) as e:
        logger.error(f"Token解码失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效凭证",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 获取用户信息
    user = db.query(User).filter(User.id == token_data.sub).first()
    if not user:
        logger.warning(f"尝试访问不存在的用户: {token_data.sub}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )
    
    # 检查用户是否激活
    if not user.is_active:
        logger.warning(f"尝试访问已停用账户: {user.email}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="账户已停用",
        )
    
    # 获取请求中的租户ID
    tenant_id = request.headers.get("X-Tenant-ID")
    if tenant_id:
        # 验证用户对租户的访问权限
        if not verify_tenant_access(user.id, tenant_id, db):
            logger.warning(f"用户 {user.email} 尝试访问无权限的租户 {tenant_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该租户",
            )
        # 将租户ID添加到用户对象中，便于后续使用
        user.current_tenant_id = int(tenant_id)
    else:
        # 如果没有指定租户ID，使用用户的第一个可访问租户或默认值1
        from app.models.user_tenant import UserTenant
        user_tenant = db.query(UserTenant).filter(UserTenant.user_id == user.id).first()
        if user_tenant:
            user.current_tenant_id = user_tenant.tenant_id
        else:
            # 如果没有关联租户，使用默认值1
            user.current_tenant_id = 1
    
    return user


def get_current_active_superuser(
    current_user: User = Depends(get_current_user),
) -> User:
    """获取当前超级管理员用户

    Args:
        current_user (User): 当前认证用户

    Raises:
        HTTPException: 权限不足异常

    Returns:
        User: 当前超级管理员用户
    """
    if not current_user.is_superuser:
        logger.warning(f"非管理员用户 {current_user.email} 尝试访问需要管理员权限的接口")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限",
        )
    return current_user


def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """获取当前活跃用户

    Args:
        current_user (User): 当前认证用户

    Returns:
        User: 当前活跃用户
    """
    return current_user


def get_current_tenant(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Any:
    """获取当前租户

    Args:
        request (Request): 请求对象
        current_user (User): 当前认证用户
        db (Session): 数据库会话

    Raises:
        HTTPException: 租户不存在或无权访问异常

    Returns:
        Any: 当前租户对象
    """
    from app.models.tenant import Tenant
    
    tenant_id = request.headers.get("X-Tenant-ID")
    if not tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="缺少租户ID",
        )
    
    tenant = db.query(Tenant).filter(Tenant.id == tenant_id).first()
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="租户不存在",
        )
    
    # 验证用户对租户的访问权限
    if not verify_tenant_access(current_user.id, tenant_id, db):
        logger.warning(f"用户 {current_user.email} 尝试访问无权限的租户 {tenant_id}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权访问该租户",
        )
    
    return tenant


def get_cached_db():
    """获取带缓存的数据库会话"""
    db = _get_db_from_cache() or SessionLocal()
    try:
        yield db
    finally:
        if not _is_cached(db):
            db.close()


def get_current_user_optional(
    request: Request,
    db: Session = Depends(get_db),
    token: Optional[str] = None,
) -> Optional[User]:
    """获取当前认证用户（可选，用于开发环境）
    
    如果没有token或token无效，返回None而不是抛出异常
    """
    from app.core.config import settings
    
    # 开发环境下，如果没有提供token，返回一个模拟用户
    if not token and settings.ENVIRONMENT == "development":
        # 创建一个模拟用户用于开发
        mock_user = User()
        mock_user.id = "dev-user-001"
        mock_user.email = "<EMAIL>"
        mock_user.is_active = True
        mock_user.is_superuser = True
        mock_user.current_tenant_id = 1
        return mock_user
    
    # 如果有token，尝试验证
    if token:
        try:
            return get_current_user(request, db, token)
        except HTTPException:
            # 如果token无效，在开发环境下返回模拟用户
            if settings.ENVIRONMENT == "development":
                mock_user = User()
                mock_user.id = "dev-user-001"
                mock_user.email = "<EMAIL>"
                mock_user.is_active = True
                mock_user.is_superuser = True
                mock_user.current_tenant_id = 1
                return mock_user
            return None
    
    return None 
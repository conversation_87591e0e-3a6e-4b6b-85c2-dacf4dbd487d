"""
工作流相关API端点
"""
from typing import Any, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_user, get_async_session
from app.models.user import User
from app.schemas.workflow import WorkflowCreate, WorkflowUpdate, WorkflowOut, WorkflowExecutionOut
from app.services.workflow_service import WorkflowService

router = APIRouter()


@router.get("/")
async def get_workflows(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
) -> Any:
    """
    获取工作流列表
    """
    # 返回模拟工作流数据用于开发测试
    workflows = []
    for i in range(min(limit, 3)):
        workflows.append({
            "id": f"workflow-{i+1}",
            "name": f"工作流 {i+1}",
            "description": f"这是第{i+1}个工作流的描述",
            "dag_definition": {"nodes": [], "edges": []},
            "trigger_config": None,
            "status": "active",
            "tenant_id": "dev-tenant",
            "created_by": "dev-user",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        })
    
    return workflows


@router.post("/", response_model=WorkflowOut)
async def create_workflow(
    workflow_data: WorkflowCreate,
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    创建新工作流
    """
    try:
        workflow = await WorkflowService.create_workflow(
            db=db,
            workflow_data=workflow_data,
            user_id=current_user.id
        )
        
        return {
            "id": str(workflow.id),
            "name": workflow.name,
            "description": workflow.description,
            "status": "active" if workflow.is_active else "inactive",
            "created_at": workflow.created_at.isoformat() if workflow.created_at else None,
            "updated_at": workflow.updated_at.isoformat() if workflow.updated_at else None,
            "user_id": str(workflow.user_id),
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建工作流失败: {str(e)}"
        )


@router.get("/{workflow_id}", response_model=WorkflowOut)
async def get_workflow(
    workflow_id: UUID,
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    获取单个工作流详情
    """
    try:
        workflow = await WorkflowService.get_workflow_by_id(
            db=db,
            workflow_id=workflow_id,
            user_id=current_user.id
        )
        
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工作流不存在"
            )
        
        return {
            "id": str(workflow.id),
            "name": workflow.name,
            "description": workflow.description,
            "status": "active" if workflow.is_active else "inactive",
            "created_at": workflow.created_at.isoformat() if workflow.created_at else None,
            "updated_at": workflow.updated_at.isoformat() if workflow.updated_at else None,
            "user_id": str(workflow.user_id),
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工作流详情失败: {str(e)}"
        )


@router.put("/{workflow_id}", response_model=WorkflowOut)
async def update_workflow(
    workflow_id: UUID,
    workflow_data: WorkflowUpdate,
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    更新工作流
    """
    try:
        workflow = await WorkflowService.update_workflow(
            db=db,
            workflow_id=workflow_id,
            workflow_data=workflow_data,
            user_id=current_user.id
        )
        
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工作流不存在"
            )
        
        return {
            "id": str(workflow.id),
            "name": workflow.name,
            "description": workflow.description,
            "status": "active" if workflow.is_active else "inactive",
            "created_at": workflow.created_at.isoformat() if workflow.created_at else None,
            "updated_at": workflow.updated_at.isoformat() if workflow.updated_at else None,
            "user_id": str(workflow.user_id),
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新工作流失败: {str(e)}"
        )


@router.delete("/{workflow_id}")
async def delete_workflow(
    workflow_id: UUID,
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    删除工作流
    """
    try:
        success = await WorkflowService.delete_workflow(
            db=db,
            workflow_id=workflow_id,
            user_id=current_user.id
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工作流不存在"
            )
        
        return {"message": "工作流删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除工作流失败: {str(e)}"
        )


@router.post("/{workflow_id}/executions", response_model=WorkflowExecutionOut)
async def execute_workflow(
    workflow_id: UUID,
    execution_data: dict,
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    执行工作流
    """
    try:
        execution = await WorkflowService.execute_workflow(
            db=db,
            workflow_id=workflow_id,
            user_id=current_user.id,
            input_data=execution_data.get("input_parameters", {})
        )
        
        return {
            "id": str(execution.id),
            "workflow_id": str(execution.workflow_id),
            "status": execution.status,
            "input_data": execution.input_data,
            "output_data": execution.output_data,
            "error_message": execution.error_message,
            "started_at": execution.started_at.isoformat() if execution.started_at else None,
            "completed_at": execution.completed_at.isoformat() if execution.completed_at else None,
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"执行工作流失败: {str(e)}"
        )


@router.get("/{workflow_id}/executions", response_model=List[WorkflowExecutionOut])
async def get_workflow_executions(
    workflow_id: UUID,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    获取工作流执行历史
    """
    try:
        executions = await WorkflowService.get_workflow_executions(
            db=db,
            workflow_id=workflow_id,
            user_id=current_user.id,
            skip=skip,
            limit=limit
        )
        
        execution_list = []
        for execution in executions:
            execution_dict = {
                "id": str(execution.id),
                "workflow_id": str(execution.workflow_id),
                "status": execution.status,
                "input_data": execution.input_data,
                "output_data": execution.output_data,
                "error_message": execution.error_message,
                "started_at": execution.started_at.isoformat() if execution.started_at else None,
                "completed_at": execution.completed_at.isoformat() if execution.completed_at else None,
            }
            execution_list.append(execution_dict)
        
        return execution_list
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工作流执行历史失败: {str(e)}"
        )

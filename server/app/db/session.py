from sqlalchemy.orm import declarative_base
from typing import AsyncGenerator, List, Optional, Dict, Any
from contextlib import asynccontextmanager
from typing import AsyncGenerator
from sqlalchemy.orm import declarative_base
"""
数据库会话
提供数据库连接和会话
"""
import os
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# 获取数据库URL
POSTGRES_USER = os.getenv("POSTGRES_USER", "postgres")
POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD", "postgres")
POSTGRES_HOST = os.getenv("POSTGRES_HOST", "postgres")
POSTGRES_PORT = os.getenv("POSTGRES_PORT", "5432")
POSTGRES_DB = os.getenv("POSTGRES_DB", "sgaspace")

DATABASE_URL = f"postgresql+asyncpg://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"

# 创建异步引擎
engine = create_async_engine(
    DATABASE_URL,
    echo=False,
    future=True,
)

# 创建会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,
)

# 创建基类
Base = declarative_base()

# 依赖项函数，用于获取数据库会话
def get_db():
    """
    获取数据库会话
    
    Returns:
        SQLAlchemy会话
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 依赖项函数，用于获取异步数据库会话
async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """
    获取异步数据库会话
    
    Returns:
        异步SQLAlchemy会话
    """
    async with SessionLocal() as session:
        yield session

# 异步会话上下文管理器，用于测试和脚本
@asynccontextmanager
async def get_async_session_context() -> AsyncGenerator[AsyncSession, None]:
    """
    异步会话上下文管理器
    
    适用于测试和脚本
    
    Yields:
        异步SQLAlchemy会话
    """
    async with SessionLocal() as session:
        try:
            yield session
        finally:
            await session.close() 
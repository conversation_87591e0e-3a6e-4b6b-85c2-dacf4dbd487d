"""
工作流相关的数据模型定义
"""
from typing import Dict, Any, List, Optional
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field


class WorkflowBase(BaseModel):
    """工作流基础模型"""
    name: str = Field(..., description="工作流名称")
    description: Optional[str] = Field(None, description="工作流描述")
    dag_definition: Dict[str, Any] = Field(..., description="DAG定义")
    trigger_config: Optional[Dict[str, Any]] = Field(None, description="触发器配置")


class WorkflowCreate(WorkflowBase):
    """创建工作流的请求模型"""
    pass


class WorkflowUpdate(BaseModel):
    """更新工作流的请求模型"""
    name: Optional[str] = Field(None, description="工作流名称")
    description: Optional[str] = Field(None, description="工作流描述")
    dag_definition: Optional[Dict[str, Any]] = Field(None, description="DAG定义")
    trigger_config: Optional[Dict[str, Any]] = Field(None, description="触发器配置")
    status: Optional[str] = Field(None, description="工作流状态")


class WorkflowOut(WorkflowBase):
    """工作流输出模型"""
    id: str = Field(..., description="工作流ID")
    status: str = Field(..., description="工作流状态")
    tenant_id: str = Field(..., description="租户ID")
    created_by: str = Field(..., description="创建者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class WorkflowExecutionCreate(BaseModel):
    """工作流执行创建模型"""
    input_parameters: Dict[str, Any] = Field(..., description="输入参数")


class WorkflowExecutionOut(BaseModel):
    """工作流执行输出模型"""
    id: str = Field(..., description="执行ID")
    workflow_id: str = Field(..., description="工作流ID")
    status: str = Field(..., description="执行状态")
    input_parameters: Dict[str, Any] = Field(..., description="输入参数")
    output_result: Optional[Dict[str, Any]] = Field(None, description="输出结果")
    error_message: Optional[str] = Field(None, description="错误信息")
    started_at: datetime = Field(..., description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")

    class Config:
        from_attributes = True
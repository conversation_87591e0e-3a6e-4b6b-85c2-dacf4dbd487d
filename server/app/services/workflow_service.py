"""
工作流服务
提供工作流管理功能
"""
from typing import List, Optional, Dict, Any
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from loguru import logger

from app.models.workflow import Workflow
from app.schemas.workflow import WorkflowCreate, WorkflowUpdate


class WorkflowService:
    """工作流服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_workflow(
        self,
        workflow_data: WorkflowCreate,
        user_id: UUID,
        tenant_id: UUID
    ) -> Workflow:
        """创建工作流"""
        try:
            workflow = Workflow(
                name=workflow_data.name,
                description=workflow_data.description,
                config=workflow_data.dag_definition,  # 使用config字段存储DAG定义
                status="draft",
                tenant_id=str(tenant_id),
                created_by=str(user_id)
            )
            
            self.db.add(workflow)
            await self.db.commit()
            await self.db.refresh(workflow)
            
            logger.info(f"创建工作流成功: {workflow.id}")
            return workflow
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建工作流失败: {e}")
            raise
    
    async def get_workflows(
        self,
        tenant_id: UUID,
        skip: int = 0,
        limit: int = 100
    ) -> List[Workflow]:
        """获取工作流列表"""
        try:
            result = await self.db.execute(
                select(Workflow)
                .where(Workflow.tenant_id == tenant_id)
                .offset(skip)
                .limit(limit)
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"获取工作流列表失败: {e}")
            raise
    
    async def get_workflow_by_id(
        self,
        workflow_id: UUID,
        tenant_id: UUID
    ) -> Optional[Workflow]:
        """根据ID获取工作流"""
        try:
            result = await self.db.execute(
                select(Workflow)
                .where(
                    Workflow.id == workflow_id,
                    Workflow.tenant_id == tenant_id
                )
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取工作流失败: {e}")
            raise
    
    async def update_workflow(
        self,
        workflow_id: UUID,
        tenant_id: UUID,
        workflow_data: WorkflowUpdate
    ) -> Optional[Workflow]:
        """更新工作流"""
        try:
            # 构建更新数据
            update_data = {}
            if workflow_data.name is not None:
                update_data["name"] = workflow_data.name
            if workflow_data.description is not None:
                update_data["description"] = workflow_data.description
            if workflow_data.dag_definition is not None:
                update_data["config"] = workflow_data.dag_definition
            if workflow_data.status is not None:
                update_data["status"] = workflow_data.status
            
            if not update_data:
                # 如果没有要更新的数据，直接返回当前工作流
                return await self.get_workflow_by_id(workflow_id, tenant_id)
            
            # 执行更新
            await self.db.execute(
                update(Workflow)
                .where(
                    Workflow.id == workflow_id,
                    Workflow.tenant_id == tenant_id
                )
                .values(**update_data)
            )
            await self.db.commit()
            
            # 返回更新后的工作流
            return await self.get_workflow_by_id(workflow_id, tenant_id)
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新工作流失败: {e}")
            raise
    
    async def delete_workflow(
        self,
        workflow_id: UUID,
        tenant_id: UUID
    ) -> bool:
        """删除工作流"""
        try:
            result = await self.db.execute(
                delete(Workflow)
                .where(
                    Workflow.id == workflow_id,
                    Workflow.tenant_id == tenant_id
                )
            )
            await self.db.commit()
            
            if result.rowcount > 0:
                logger.info(f"删除工作流成功: {workflow_id}")
                return True
            else:
                logger.warning(f"工作流不存在或无权删除: {workflow_id}")
                return False
        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除工作流失败: {e}")
            raise
    
    async def execute_workflow(
        self,
        workflow_id: UUID,
        tenant_id: UUID,
        input_parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """执行工作流"""
        try:
            # 获取工作流
            workflow = await self.get_workflow_by_id(workflow_id, tenant_id)
            if not workflow:
                raise ValueError(f"工作流不存在: {workflow_id}")
            
            # 检查工作流状态
            if workflow.status != "active":
                raise ValueError(f"工作流状态不允许执行: {workflow.status}")
            
            # TODO: 实现具体的工作流执行逻辑
            # 这里可以集成具体的工作流引擎，如 Airflow、Temporal 等
            
            logger.info(f"执行工作流: {workflow_id}")
            return {
                "execution_id": str(UUID()),
                "status": "started",
                "workflow_id": str(workflow_id),
                "input_parameters": input_parameters
            }
        except Exception as e:
            logger.error(f"执行工作流失败: {e}")
            raise
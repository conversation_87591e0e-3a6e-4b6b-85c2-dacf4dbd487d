"""
向量数据库服务 - 提供向量存储和检索功能
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
import uuid
import time
from datetime import datetime

from pymilvus import (
    connections,
    Collection,
    CollectionSchema,
    FieldSchema,
    DataType,
    utility
)

from app.core.config import settings
from app.commons.exceptions import ServiceException

# 配置日志
logger = logging.getLogger(__name__)


class VectorStore:
    """向量数据库服务，提供向量存储和检索功能"""
    
    def __init__(self):
        """初始化向量数据库服务"""
        self.connected = False
        try:
            # 连接Milvus服务器
            connections.connect(
                alias="default",
                host=settings.MILVUS_HOST,
                port=settings.MILVUS_PORT
            )
            self.connected = True
            logger.info("成功连接到Milvus服务器")
        except Exception as e:
            logger.warning(f"连接Milvus服务器失败: {str(e)}，将在需要时重试连接")
            self.connected = False
    
    def _ensure_connection(self):
        """确保Milvus连接可用"""
        if not self.connected:
            try:
                connections.connect(
                    alias="default",
                    host=settings.MILVUS_HOST,
                    port=settings.MILVUS_PORT
                )
                self.connected = True
                logger.info("成功连接到Milvus服务器")
            except Exception as e:
                logger.warning(f"连接Milvus服务器失败: {str(e)}，向量功能将不可用")
                self.connected = False
                # 不抛出异常，让服务继续运行
    
    async def create_collection(
        self,
        collection_name: str,
        dimension: int,
        description: str = "",
        metric_type: str = "COSINE"
    ) -> None:
        """创建向量集合
        
        Args:
            collection_name: 集合名称
            dimension: 向量维度
            description: 集合描述
            metric_type: 相似度度量类型，可选值：L2/IP/COSINE
            
        Raises:
            ServiceException: 当创建集合失败时
        """
        # 检查连接状态
        if not self.connected:
            self._ensure_connection()
            if not self.connected:
                logger.warning("Milvus未连接，跳过创建集合操作")
                return
                
        try:
            # 检查集合是否已存在
            if utility.has_collection(collection_name):
                logger.warning(f"集合 {collection_name} 已存在")
                return
            
            # 定义字段
            fields = [
                FieldSchema(
                    name="id",
                    dtype=DataType.VARCHAR,
                    is_primary=True,
                    max_length=36
                ),
                FieldSchema(
                    name="tenant_id",
                    dtype=DataType.VARCHAR,
                    max_length=36
                ),
                FieldSchema(
                    name="knowledge_base_id",
                    dtype=DataType.VARCHAR,
                    max_length=36
                ),
                FieldSchema(
                    name="document_id",
                    dtype=DataType.VARCHAR,
                    max_length=36
                ),
                FieldSchema(
                    name="chunk_id",
                    dtype=DataType.VARCHAR,
                    max_length=36
                ),
                FieldSchema(
                    name="vector",
                    dtype=DataType.FLOAT_VECTOR,
                    dim=dimension
                ),
                FieldSchema(
                    name="metadata",
                    dtype=DataType.JSON
                ),
                FieldSchema(
                    name="created_at",
                    dtype=DataType.VARCHAR,
                    max_length=30
                )
            ]
            
            # 创建集合模式
            schema = CollectionSchema(
                fields=fields,
                description=description
            )
            
            # 创建集合
            collection = Collection(
                name=collection_name,
                schema=schema,
                using="default"
            )
            
            # 创建索引
            index_params = {
                "metric_type": metric_type,
                "index_type": "IVF_FLAT",
                "params": {"nlist": 1024}
            }
            collection.create_index(
                field_name="vector",
                index_params=index_params
            )
            
            logger.info(f"成功创建集合 {collection_name}")
            
        except Exception as e:
            logger.error(f"创建集合失败: {str(e)}")
            raise ServiceException(f"创建集合失败: {str(e)}")
    
    async def insert_vectors(
        self,
        collection_name: str,
        vectors: List[List[float]],
        tenant_id: uuid.UUID,
        knowledge_base_id: uuid.UUID,
        document_id: uuid.UUID,
        chunk_ids: List[uuid.UUID],
        metadata: Optional[List[Dict[str, Any]]] = None
    ) -> List[str]:
        """插入向量数据
        
        Args:
            collection_name: 集合名称
            vectors: 向量列表
            tenant_id: 租户ID
            knowledge_base_id: 知识库ID
            document_id: 文档ID
            chunk_ids: 文本块ID列表
            metadata: 元数据列表
            
        Returns:
            List[str]: 插入的记录ID列表
            
        Raises:
            ServiceException: 当插入数据失败时
        """
        try:
            # 获取集合
            collection = Collection(collection_name)
            collection.load()
            
            # 准备数据
            now = datetime.utcnow().isoformat()
            ids = [str(uuid.uuid4()) for _ in range(len(vectors))]
            
            data = {
                "id": ids,
                "tenant_id": [str(tenant_id)] * len(vectors),
                "knowledge_base_id": [str(knowledge_base_id)] * len(vectors),
                "document_id": [str(document_id)] * len(vectors),
                "chunk_id": [str(chunk_id) for chunk_id in chunk_ids],
                "vector": vectors,
                "metadata": metadata or [{}] * len(vectors),
                "created_at": [now] * len(vectors)
            }
            
            # 插入数据
            collection.insert(data)
            
            logger.info(f"成功插入 {len(vectors)} 条向量数据")
            return ids
            
        except Exception as e:
            logger.error(f"插入向量数据失败: {str(e)}")
            raise ServiceException(f"插入向量数据失败: {str(e)}")
    
    async def search_similar(
        self,
        collection_name: str,
        query_vector: List[float],
        tenant_id: uuid.UUID,
        knowledge_base_id: Optional[uuid.UUID] = None,
        limit: int = 10,
        min_score: float = 0.0,
        filter_expr: Optional[str] = None,
        output_fields: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """搜索相似向量
        
        Args:
            collection_name: 集合名称
            query_vector: 查询向量
            tenant_id: 租户ID
            knowledge_base_id: 知识库ID，如果提供，则只搜索特定知识库
            limit: 返回结果数量限制
            min_score: 最小相似度阈值
            filter_expr: 额外的过滤表达式
            output_fields: 返回的字段列表，默认返回所有字段
            
        Returns:
            List[Dict[str, Any]]: 相似向量结果列表，每个结果包含向量ID、相似度分数、元数据等
            
        Raises:
            ServiceException: 当搜索失败时
        """
        try:
            # 检查集合是否存在
            if not utility.has_collection(collection_name):
                logger.warning(f"集合 {collection_name} 不存在")
                return []
            
            # 获取集合
            collection = Collection(collection_name)
            collection.load()
            
            # 构建过滤条件
            base_expr = f'tenant_id == "{str(tenant_id)}"'
            if knowledge_base_id:
                base_expr += f' && knowledge_base_id == "{str(knowledge_base_id)}"'
            
            # 如果有额外过滤条件，合并过滤表达式
            expr = base_expr
            if filter_expr:
                expr = f"({base_expr}) && ({filter_expr})"
            
            # 如果未指定输出字段，返回所有字段
            if not output_fields:
                output_fields = ["id", "tenant_id", "knowledge_base_id", "document_id", 
                                "chunk_id", "metadata", "created_at"]
            
            # 设置搜索参数
            search_params = {
                "metric_type": "COSINE",  # 使用余弦相似度
                "params": {"nprobe": 10}  # 搜索时使用的探针数量
            }
            
            # 执行搜索
            start_time = time.time()
            results = collection.search(
                data=[query_vector],
                anns_field="vector",
                param=search_params,
                limit=limit,
                expr=expr,
                output_fields=output_fields
            )
            elapsed_time = time.time() - start_time
            
            # 处理结果
            search_results = []
            if results and len(results) > 0:
                ids = results[0].ids
                distances = results[0].distances
                fields = results[0].fields
                
                for i in range(len(ids)):
                    # 余弦相似度，将距离转换为相似度分数
                    score = 1.0 - distances[i]
                    
                    # 只返回超过最小相似度阈值的结果
                    if score < min_score:
                        continue
                    
                    # 构建结果对象
                    result = {
                        "id": ids[i],
                        "score": score
                    }
                    
                    # 添加其他字段
                    for field_name, field_data in fields.items():
                        result[field_name] = field_data[i]
                    
                    search_results.append(result)
            
            logger.info(f"搜索完成，耗时: {elapsed_time:.4f}秒，找到 {len(search_results)} 条结果")
            return search_results
            
        except Exception as e:
            logger.error(f"搜索相似向量失败: {str(e)}")
            raise ServiceException(f"搜索相似向量失败: {str(e)}")
    
    async def delete_vectors(
        self,
        collection_name: str,
        tenant_id: uuid.UUID,
        knowledge_base_id: Optional[uuid.UUID] = None,
        document_id: Optional[uuid.UUID] = None
    ) -> int:
        """删除向量数据
        
        Args:
            collection_name: 集合名称
            tenant_id: 租户ID
            knowledge_base_id: 知识库ID，如果提供，则只删除特定知识库的向量
            document_id: 文档ID，如果提供，则只删除特定文档的向量
            
        Returns:
            int: 删除的记录数量
            
        Raises:
            ServiceException: 当删除失败时
        """
        try:
            # 检查集合是否存在
            if not utility.has_collection(collection_name):
                logger.warning(f"集合 {collection_name} 不存在")
                return 0
            
            # 获取集合
            collection = Collection(collection_name)
            collection.load()
            
            # 构建过滤条件
            expr = f'tenant_id == "{str(tenant_id)}"'
            if knowledge_base_id:
                expr += f' && knowledge_base_id == "{str(knowledge_base_id)}"'
            if document_id:
                expr += f' && document_id == "{str(document_id)}"'
            
            # 执行删除
            result = collection.delete(expr)
            deleted_count = result.delete_count
            
            logger.info(f"成功删除 {deleted_count} 条向量数据")
            return deleted_count
            
        except Exception as e:
            logger.error(f"删除向量数据失败: {str(e)}")
            raise ServiceException(f"删除向量数据失败: {str(e)}")
    
    async def recreate_index(
        self,
        collection_name: str,
        metric_type: str = "COSINE",
        index_type: str = "IVF_FLAT"
    ) -> None:
        """重新创建索引
        
        Args:
            collection_name: 集合名称
            metric_type: 相似度度量类型，可选值：L2/IP/COSINE
            index_type: 索引类型，可选值：FLAT/IVF_FLAT/IVF_SQ8/HNSW等
            
        Raises:
            ServiceException: 当创建索引失败时
        """
        try:
            # 检查集合是否存在
            if not utility.has_collection(collection_name):
                logger.warning(f"集合 {collection_name} 不存在")
                return
            
            # 获取集合
            collection = Collection(collection_name)
            
            # 删除现有索引
            collection.drop_index()
            
            # 创建新索引
            index_params = {
                "metric_type": metric_type,
                "index_type": index_type,
                "params": {"nlist": 1024} if index_type == "IVF_FLAT" else {"M": 8, "efConstruction": 200}
            }
            collection.create_index(
                field_name="vector",
                index_params=index_params
            )
            
            logger.info(f"成功重建集合 {collection_name} 的索引")
            
        except Exception as e:
            logger.error(f"重建索引失败: {str(e)}")
            raise ServiceException(f"重建索引失败: {str(e)}")
    
    def __del__(self):
        """析构函数，断开与Milvus的连接"""
        try:
            connections.disconnect(alias="default")
            logger.info("断开与Milvus服务器的连接")
        except Exception as e:
            logger.error(f"断开连接失败: {str(e)}")

/**
 * API服务
 * 用于统一处理API请求，避免硬编码
 */
import { apiConfig } from '../config/apiConfig';
import { API_PATHS, buildApiUrl, buildResourceUrl, buildLlmUrl, buildCamelSessionMessagesUrl } from '../constants/apiPaths';
import { isElectron } from '../utils/environment';
import type { Agent, Workflow, Document, Settings, LocalModel, WindowState } from '../types/api';

// 请求选项接口
interface RequestOptions {
  headers?: Record<string, string>;
  timeout?: number;
  signal?: AbortSignal;
}

// 响应接口
interface ApiResponse<T> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

// LLM请求接口
interface LlmRequest {
  prompt: string;
  max_tokens?: number;
  temperature?: number;
  use_local?: boolean;
}

// LLM响应接口
interface LlmResponse {
  status: 'success' | 'error';
  result: string;
  usage: {
    tokens: number;
  };
  source: 'cloud' | 'local';
}

// CAMEL会话创建请求接口
interface CamelSessionRequest {
  roles: Array<'task_planning_expert' | 'knowledge_management_expert' | 'tool_integration_expert' | 'decision_support_expert' | 'collaboration_expert'>;
  task: string;
  model_config?: {
    model_type: 'gpt_4_turbo' | 'local_llama3' | 'local_mistral';
    temperature?: number;
  };
}

// CAMEL会话响应接口
interface CamelSessionResponse {
  session_id: string;
  status: 'created' | 'active' | 'completed' | 'failed';
}

// CAMEL消息请求接口
interface CamelMessageRequest {
  role: 'task_planning_expert' | 'knowledge_management_expert' | 'tool_integration_expert' | 'decision_support_expert' | 'collaboration_expert' | 'human';
  content: string;
}

// CAMEL消息响应接口
interface CamelMessageResponse {
  message_id: string;
  timestamp: string;
}

/**
 * API服务类
 * 提供统一的API请求方法
 */
class ApiService {
  private authToken: string | null = null;
  
  /**
   * 设置认证令牌
   * @param token JWT令牌
   */
  setAuthToken(token: string): void {
    this.authToken = token;
  }
  
  /**
   * 清除认证令牌
   */
  clearAuthToken(): void {
    this.authToken = null;
  }
  
  /**
   * 获取认证头
   * @returns 认证头对象
   */
  private getAuthHeaders(): Record<string, string> {
    if (this.authToken) {
      return {
        'Authorization': `Bearer ${this.authToken}`
      };
    }
    return {};
  }
  
  /**
   * 发送GET请求
   * @param url 请求URL
   * @param options 请求选项
   * @returns Promise<ApiResponse<T>>
   */
  async get<T>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>(url, 'GET', undefined, options);
  }

  /**
   * 发送POST请求
   * @param url 请求URL
   * @param data 请求数据
   * @param options 请求选项
   * @returns Promise<ApiResponse<T>>
   */
  async post<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>(url, 'POST', data, options);
  }

  /**
   * 发送PUT请求
   * @param url 请求URL
   * @param data 请求数据
   * @param options 请求选项
   * @returns Promise<ApiResponse<T>>
   */
  async put<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>(url, 'PUT', data, options);
  }

  /**
   * 发送PATCH请求
   * @param url 请求URL
   * @param data 请求数据
   * @param options 请求选项
   * @returns Promise<ApiResponse<T>>
   */
  async patch<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>(url, 'PATCH', data, options);
  }

  /**
   * 发送DELETE请求
   * @param url 请求URL
   * @param options 请求选项
   * @returns Promise<ApiResponse<T>>
   */
  async delete<T>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    return this.request<T>(url, 'DELETE', undefined, options);
  }

  /**
   * 通用请求方法
   * @param url 请求URL
   * @param method 请求方法
   * @param data 请求数据
   * @param options 请求选项
   * @returns Promise<ApiResponse<T>>
   */
  private async request<T>(
    url: string,
    method: string,
    data?: any,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    // 构建完整的URL
    const fullUrl = url.startsWith('http') ? url : `${apiConfig.baseUrl}${url}`;
    
    // 创建请求控制器，用于超时处理
    const controller = new AbortController();
    const signal = options?.signal || controller.signal;
    
    // 设置超时
    const timeout = options?.timeout || apiConfig.timeout;
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    try {
      // 准备请求头
      const headers = {
        'Content-Type': 'application/json',
        ...this.getAuthHeaders(),
        ...options?.headers,
      };
      
      // 如果有数据，添加Content-Type
      if (data && !headers['Content-Type']) {
        headers['Content-Type'] = 'application/json';
      }
      
      // 发送请求
      const response = await fetch(fullUrl, {
        method,
        headers,
        body: data ? JSON.stringify(data) : undefined,
        signal,
      });
      
      // 解析响应
      const responseData = await response.json();
      
      // 获取响应头
      const responseHeaders: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });
      
      return {
        data: responseData as T,
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
      };
    } finally {
      clearTimeout(timeoutId);
    }
  }
  
  /**
   * 创建WebSocket连接
   * @param tenantId 租户ID
   * @param windowId 可选的窗口ID
   * @param onMessage 消息处理函数
   * @param onError 错误处理函数
   * @returns WebSocket实例
   */
  createWebSocket(
    tenantId: string,
    windowId?: string,
    onMessage?: (event: MessageEvent) => void,
    onError?: (event: Event) => void
  ): WebSocket {
    // 构建WebSocket URL
    let wsUrl = `${apiConfig.wsUrl}/${apiConfig.version}/ws?tenant_id=${tenantId}`;
    
    if (windowId) {
      wsUrl += `&window_id=${windowId}`;
    }
    
    // 创建WebSocket
    const ws = new WebSocket(wsUrl);
    
    // 添加认证头（如果有）
    if (this.authToken) {
      ws.onopen = () => {
        ws.send(JSON.stringify({
          type: 'auth',
          token: this.authToken
        }));
      };
    }
    
    // 添加消息处理函数
    if (onMessage) {
      ws.onmessage = onMessage;
    }
    
    // 添加错误处理函数
    if (onError) {
      ws.onerror = onError;
    }
    
    return ws;
  }
}

// 创建API服务实例
const apiService = new ApiService();

export default apiService;

// 导出便捷API方法
export const api = {
  /**
   * 租户相关API
   */
  tenants: {
    /**
     * 获取租户列表
     * @returns Promise<ApiResponse<Array<any>>>
     */
    getAll: () => {
      return apiService.get<Array<any>>(API_PATHS.TENANTS);
    },
    
    /**
     * 创建租户
     * @param data 租户数据
     * @returns Promise<ApiResponse<any>>
     */
    create: (data: { name: string; domain: string }) => {
      return apiService.post<any>(API_PATHS.TENANTS, data);
    },
    
    /**
     * 获取单个租户
     * @param tenantId 租户ID
     * @returns Promise<ApiResponse<any>>
     */
    getById: (tenantId: string) => {
      return apiService.get<any>(buildResourceUrl('TENANTS', tenantId));
    }
  },
  
  /**
   * 代理相关API
   */
  agents: {
    /**
     * 获取代理列表
     * @param params 查询参数
     * @returns Promise<ApiResponse<Agent[]>>
     */
    getAll: (params?: Record<string, string | number | boolean>) => {
      return apiService.get<Agent[]>(buildApiUrl('AGENTS', params));
    },
    
    /**
     * 获取单个代理
     * @param agentId 代理ID
     * @returns Promise<ApiResponse<Agent>>
     */
    getById: (agentId: string) => {
      return apiService.get<Agent>(buildResourceUrl('AGENTS', agentId));
    },
    
    /**
     * 创建代理
     * @param data 代理数据
     * @returns Promise<ApiResponse<Agent>>
     */
    create: (data: {
      name: string;
      role: string;
      camel_config?: {
        task_type: string;
        model_type: string;
        temperature?: number;
      }
    }) => {
      return apiService.post<Agent>(API_PATHS.AGENTS, data);
    }
  },
  
  /**
   * 工作流相关API
   */
  workflows: {
    /**
     * 获取工作流列表
     * @param params 查询参数
     * @returns Promise<ApiResponse<Workflow[]>>
     */
    getAll: (params?: Record<string, string | number | boolean>) => {
      return apiService.get<Workflow[]>(buildApiUrl('WORKFLOWS', params));
    },
    
    /**
     * 获取单个工作流
     * @param workflowId 工作流ID
     * @returns Promise<ApiResponse<Workflow>>
     */
    getById: (workflowId: string) => {
      return apiService.get<Workflow>(buildResourceUrl('WORKFLOWS', workflowId));
    },
    
    /**
     * 创建工作流
     * @param data 工作流数据
     * @returns Promise<ApiResponse<Workflow>>
     */
    create: (data: {
      name: string;
      description?: string;
      dag_definition: any;
      trigger_config?: any;
    }) => {
      return apiService.post<Workflow>(API_PATHS.WORKFLOWS, data);
    },
    
    /**
     * 执行工作流
     * @param workflowId 工作流ID
     * @param data 执行参数
     * @returns Promise<ApiResponse<any>>
     */
    execute: (workflowId: string, data: { input_parameters: any }) => {
      return apiService.post<any>(buildResourceUrl('WORKFLOWS', workflowId, 'executions'), data);
    }
  },
  
  /**
   * 知识库相关API
   */
  knowledge: {
    /**
     * 获取知识库列表
     * @param params 查询参数
     * @returns Promise<ApiResponse<Document[]>>
     */
    getAll: (params?: Record<string, string | number | boolean>) => {
      return apiService.get<Document[]>(buildApiUrl('KNOWLEDGE', params));
    },
    
    /**
     * 获取单个知识项
     * @param documentId 文档ID
     * @returns Promise<ApiResponse<Document>>
     */
    getById: (documentId: string) => {
      return apiService.get<Document>(buildResourceUrl('KNOWLEDGE', documentId));
    }
  },
  
  /**
   * 设置相关API
   */
  settings: {
    /**
     * 获取设置
     * @returns Promise<ApiResponse<Settings>>
     */
    get: () => {
      return apiService.get<Settings>(API_PATHS.SETTINGS);
    },
    
    /**
     * 更新设置
     * @param data 设置数据
     * @returns Promise<ApiResponse<Settings>>
     */
    update: (data: Partial<Settings>) => {
      return apiService.put<Settings>(API_PATHS.SETTINGS, data);
    }
  },
  
  /**
   * 大模型API
   */
  llm: {
    /**
     * 调用大模型
     * @param provider 提供商
     * @param model 模型
     * @param data 请求数据
     * @returns Promise<ApiResponse<LlmResponse>>
     */
    call: (
      provider: 'openai' | 'xunfei' | 'local' | 'camel',
      model: string,
      data: LlmRequest
    ) => {
      return apiService.post<LlmResponse>(buildLlmUrl(provider, model), data);
    }
  },
  
  /**
   * CAMEL会话相关API
   */
  camel: {
    /**
     * 创建CAMEL会话
     * @param data 会话数据
     * @returns Promise<ApiResponse<CamelSessionResponse>>
     */
    createSession: (data: CamelSessionRequest) => {
      return apiService.post<CamelSessionResponse>(API_PATHS.CAMEL_SESSIONS, data);
    },
    
    /**
     * 发送CAMEL会话消息
     * @param sessionId 会话ID
     * @param data 消息数据
     * @returns Promise<ApiResponse<CamelMessageResponse>>
     */
    sendMessage: (sessionId: string, data: CamelMessageRequest) => {
      return apiService.post<CamelMessageResponse>(buildCamelSessionMessagesUrl(sessionId), data);
    }
  },
  
  /**
   * 桌面应用相关API
   */
  desktop: {
    /**
     * 获取窗口状态
     * @returns Promise<ApiResponse<WindowState[]>>
     */
    getWindows: () => {
      return apiService.get<WindowState[]>(API_PATHS.WINDOWS);
    },
    
    /**
     * 更新窗口状态
     * @param windowId 窗口ID
     * @param data 窗口数据
     * @returns Promise<ApiResponse<WindowState>>
     */
    updateWindow: (windowId: string, data: { status: 'active' | 'inactive' | 'loading' }) => {
      return apiService.patch<WindowState>(buildResourceUrl('WINDOWS', windowId), data);
    },
    
    /**
     * 获取本地模型列表
     * @returns Promise<ApiResponse<LocalModel[]>>
     */
    getLocalModels: () => {
      return apiService.get<LocalModel[]>(API_PATHS.LOCAL_MODELS);
    },
    
    /**
     * 加载本地模型
     * @param data 模型数据
     * @returns Promise<ApiResponse<LocalModel>>
     */
    loadLocalModel: (data: {
      model_path: string;
      model_name: string;
      quantization?: 'none' | 'int8' | 'int4';
    }) => {
      return apiService.post<LocalModel>(API_PATHS.LOCAL_MODELS, data);
    }
  },
  
  /**
   * 创建WebSocket连接
   * @param tenantId 租户ID
   * @param windowId 可选的窗口ID
   * @param onMessage 消息处理函数
   * @param onError 错误处理函数
   * @returns WebSocket实例
   */
  createWebSocket: (
    tenantId: string,
    windowId?: string,
    onMessage?: (event: MessageEvent) => void,
    onError?: (event: Event) => void
  ) => {
    return apiService.createWebSocket(tenantId, windowId, onMessage, onError);
  }
};
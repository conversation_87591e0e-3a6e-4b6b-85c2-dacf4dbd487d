import React, { useEffect, useState } from 'react';
import { useAgents, useUI } from '../store/appStore';
import { useElectronAPI } from '../hooks/useElectronAPI';
import { toast } from '../components/Toast';
import { globalLoading } from '../components/Loading';
import type { Agent } from '../types/api';

interface AgentWithExtras extends Agent {
  lastActivity?: string;
  history?: any[];
}

const AgentWorkspace: React.FC = () => {
  const { electronAPI } = useElectronAPI();
  const {
    agents,
    selectedAgent,
    loading,
    error,
    fetchAgents,
    createAgent,
    updateAgent,
    deleteAgent,
    selectAgent,
    clearError,
  } = useAgents();
  
  const { setLoading, setError, setSuccessMessage } = useUI();
  
  // 简单的错误处理和异步包装函数
  const handleError = (error: any, context: string) => {
    console.error(`Error in ${context}:`, error);
    setError(error?.message || `${context} failed`);
  };
  
  const withAsyncHandling = async (fn: () => Promise<void>, context: string) => {
    try {
      await fn();
    } catch (error) {
      handleError(error, context);
      throw error;
    }
  };

  // 格式化最后活动时间
  const formatLastActivity = (timestamp: string): string => {
    const now = new Date();
    const activityTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - activityTime.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return '刚刚';
    if (diffInMinutes < 60) return `${diffInMinutes}分钟前`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}小时前`;
    return `${Math.floor(diffInMinutes / 1440)}天前`;
  };

  // 加载智能体列表
  const loadAgents = async () => {
    try {
      globalLoading.show('正在加载智能体...');
      await withAsyncHandling(async () => {
        await fetchAgents();
      }, 'loadAgents');
    } catch (error) {
      console.error('加载智能体失败:', error);
      // 如果API调用失败，尝试使用Electron API
      if (electronAPI?.agents?.list) {
        try {
          const agentList = await electronAPI.agents.list();
          // 这里可以将Electron API的数据转换为标准格式
          // 但为了保持一致性，我们主要依赖API服务
        } catch (electronError) {
          console.error('Electron API调用失败:', electronError);
        }
      }
    } finally {
      globalLoading.hide();
    }
  };

  // 创建新智能体
  const handleCreateAgent = async (agentData: {
    name: string;
    type: string;
    capabilities: string[];
  }) => {
    try {
      globalLoading.show('正在创建智能体...');
      
      await withAsyncHandling(async () => {
        const newAgentData = {
          name: agentData.name,
          description: `${agentData.name}的描述`,
          type: agentData.type,
          role: 'task_planning_expert' as const,
          capabilities: agentData.capabilities,
          config: {},
          status: 'active' as const,
          tenantId: 'default-tenant'
        };

        await createAgent(newAgentData);
        toast.success('创建成功', '智能体创建成功');
        setSuccessMessage('智能体创建成功');
      }, 'createAgent');
      
    } catch (error) {
      console.error('创建智能体失败:', error);
      toast.error('创建失败', '智能体创建失败，请重试');
    } finally {
      globalLoading.hide();
    }
  };

  // 删除智能体
  const handleDeleteAgent = async (agentId: string) => {
    if (!confirm('确定要删除这个智能体吗？')) {
      return;
    }

    try {
      globalLoading.show('正在删除智能体...');
      
      await withAsyncHandling(async () => {
        await deleteAgent(agentId);
        toast.success('删除成功', '智能体删除成功');
        setSuccessMessage('智能体删除成功');
      }, 'deleteAgent');
      
    } catch (error) {
      console.error('删除智能体失败:', error);
      toast.error('删除失败', '智能体删除失败，请重试');
    } finally {
      globalLoading.hide();
    }
  };

  // 选择智能体
  const handleSelectAgent = async (agent: AgentWithExtras) => {
    try {
      selectAgent(agent);
      
      // 可以在这里添加额外的智能体详情获取逻辑
    } catch (error) {
      console.error('选择智能体失败:', error);
      handleError(error, 'selectAgent');
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadAgents();
  }, []);

  // 处理错误状态
  useEffect(() => {
    if (error) {
      setError(error);
      toast.error('错误', error);
    }
  }, [error]);

  // 状态管理
  const [showCreateModal, setShowCreateModal] = useState(false);

  // 创建智能体模态框组件
  const CreateAgentModal: React.FC<{
    isOpen: boolean;
    onClose: () => void;
    onSubmit: (data: { name: string; type: string; capabilities: string[] }) => void;
  }> = ({ isOpen, onClose, onSubmit }) => {
    const [name, setName] = useState('');
    const [type, setType] = useState('');
    const [capabilities, setCapabilities] = useState<string[]>([]);
    const [capabilitiesInput, setCapabilitiesInput] = useState('');

    if (!isOpen) return null;

    const handleSubmit = () => {
      if (!name.trim() || !type.trim()) {
        toast.error('输入错误', '请填写智能体名称和类型');
        return;
      }

      onSubmit({
        name: name.trim(),
        type: type.trim(),
        capabilities,
      });

      // 重置表单
      setName('');
      setType('');
      setCapabilities([]);
      setCapabilitiesInput('');
      onClose();
    };

    const handleCapabilitiesChange = (value: string) => {
      setCapabilitiesInput(value);
      const caps = value.split(',').map(c => c.trim()).filter(c => c);
      setCapabilities(caps);
    };

    return (
      <div className="modal-overlay">
        <div className="modal">
          <div className="modal-header">
            <h2>创建新智能体</h2>
            <button className="close-btn" onClick={onClose}>×</button>
          </div>
          
          <div className="modal-content">
            <div className="form-group">
              <label>智能体名称*</label>
              <input
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="请输入智能体名称"
              />
            </div>
            
            <div className="form-group">
              <label>智能体类型*</label>
              <select value={type} onChange={(e) => setType(e.target.value)}>
                <option value="">请选择类型</option>
                <option value="task_planning">任务规划</option>
                <option value="knowledge_management">知识管理</option>
                <option value="tool_integration">工具集成</option>
                <option value="decision_support">决策支持</option>
                <option value="collaboration">协作助手</option>
              </select>
            </div>
            
            <div className="form-group">
              <label>能力标签</label>
              <input
                type="text"
                value={capabilitiesInput}
                onChange={(e) => handleCapabilitiesChange(e.target.value)}
                placeholder="请输入能力标签，用逗号分隔"
              />
              {capabilities.length > 0 && (
                <div className="capabilities-preview">
                  <span>预览: </span>
                  {capabilities.map((cap, index) => (
                    <span key={index} className="capability-tag">
                      {cap}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
          
          <div className="modal-footer">
            <button className="cancel-btn" onClick={onClose}>取消</button>
            <button className="create-btn" onClick={handleSubmit}>创建</button>
          </div>
        </div>
      </div>
    );
  };

  // 渲染主界面
  return (
    <div className="agent-workspace">
      <div className="workspace-header">
        <h1>智能体工作台</h1>
        <div className="header-actions">
          <button 
            className="refresh-btn"
            onClick={loadAgents}
            disabled={loading}
          >
            {loading ? '刷新中...' : '刷新'}
          </button>
          <button 
            className="create-agent-btn"
            onClick={() => setShowCreateModal(true)}
          >
            创建智能体
          </button>
        </div>
      </div>

      <div className="workspace-content">
        {/* 智能体列表 */}
        <div className="agents-sidebar">
          <div className="sidebar-header">
            <h2>智能体列表</h2>
            <span className="agent-count">({agents.length})</span>
          </div>
          
          {loading ? (
            <div className="loading">
              <div className="loading-spinner" />
              <span>加载中...</span>
            </div>
          ) : agents.length === 0 ? (
            <div className="empty-state">
              <div className="empty-icon">🤖</div>
              <p>暂无智能体</p>
              <button onClick={() => setShowCreateModal(true)}>
                创建第一个智能体
              </button>
            </div>
          ) : (
            <div className="agents-list">
              {agents.map((agent) => (
                <div
                  key={agent.id}
                  className={`agent-item ${selectedAgent?.id === agent.id ? 'selected' : ''}`}
                  onClick={() => handleSelectAgent(agent as AgentWithExtras)}
                >
                  <div className="agent-info">
                    <div className="agent-header">
                      <h3>{agent.name}</h3>
                      <span className={`status ${agent.status}`}>
                        {agent.status === 'active' ? '活跃' : 
                         agent.status === 'inactive' ? '空闲' : '错误'}
                      </span>
                    </div>
                    <p className="agent-description">{agent.description}</p>
                    <div className="agent-meta">
                      <span className="agent-type">{agent.type}</span>
                      <span className="last-activity">
                        {formatLastActivity(agent.updatedAt)}
                      </span>
                    </div>
                    {agent.capabilities.length > 0 && (
                      <div className="agent-capabilities">
                        {agent.capabilities.slice(0, 3).map((capability, index) => (
                          <span key={index} className="capability-tag">
                            {capability}
                          </span>
                        ))}
                        {agent.capabilities.length > 3 && (
                          <span className="more-capabilities">
                            +{agent.capabilities.length - 3}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                  <div className="agent-actions">
                    <button 
                      className="delete-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteAgent(agent.id);
                      }}
                    >
                      删除
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 智能体详情 */}
        <div className="agent-details">
          {selectedAgent ? (
            <div className="agent-details-content">
              <div className="agent-header">
                <h2>{selectedAgent.name}</h2>
                <span className={`status ${selectedAgent.status}`}>
                  {selectedAgent.status === 'active' ? '活跃' : 
                   selectedAgent.status === 'inactive' ? '空闲' : '错误'}
                </span>
              </div>
              
              <div className="agent-info-section">
                <h3>基本信息</h3>
                <div className="info-grid">
                  <div className="info-item">
                    <label>类型:</label>
                    <span>{selectedAgent.type}</span>
                  </div>
                  <div className="info-item">
                    <label>角色:</label>
                    <span>{selectedAgent.role}</span>
                  </div>
                  <div className="info-item">
                    <label>创建时间:</label>
                    <span>{new Date(selectedAgent.createdAt).toLocaleString()}</span>
                  </div>
                  <div className="info-item">
                    <label>最后更新:</label>
                    <span>{new Date(selectedAgent.updatedAt).toLocaleString()}</span>
                  </div>
                </div>
              </div>

              <div className="capabilities-section">
                <h3>能力列表</h3>
                {selectedAgent.capabilities.length > 0 ? (
                  <div className="capabilities-list">
                    {selectedAgent.capabilities.map((capability, index) => (
                      <span key={index} className="capability-tag">
                        {capability}
                      </span>
                    ))}
                  </div>
                ) : (
                  <p className="no-capabilities">暂无能力标签</p>
                )}
              </div>

              <div className="agent-description-section">
                <h3>描述</h3>
                <p>{selectedAgent.description}</p>
              </div>
            </div>
          ) : (
            <div className="no-selection">
              <div className="no-selection-icon">👈</div>
              <p>请选择一个智能体查看详情</p>
              {agents.length === 0 && (
                <button onClick={() => setShowCreateModal(true)}>
                  创建智能体
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 创建智能体模态框 */}
      <CreateAgentModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateAgent}
      />

      <style>{`
        .agent-workspace {
          height: 100vh;
          display: flex;
          flex-direction: column;
          background-color: #f8fafc;
        }

        .workspace-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px 24px;
          background-color: white;
          border-bottom: 1px solid #e2e8f0;
        }

        .workspace-header h1 {
          margin: 0;
          color: #1e293b;
          font-size: 24px;
          font-weight: 600;
        }

        .header-actions {
          display: flex;
          gap: 12px;
        }

        .refresh-btn, .create-agent-btn {
          padding: 8px 16px;
          border: none;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .refresh-btn {
          background-color: #f1f5f9;
          color: #64748b;
        }

        .refresh-btn:hover:not(:disabled) {
          background-color: #e2e8f0;
        }

        .refresh-btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .create-agent-btn {
          background-color: #3b82f6;
          color: white;
        }

        .create-agent-btn:hover {
          background-color: #2563eb;
        }

        .workspace-content {
          display: flex;
          flex: 1;
          overflow: hidden;
        }

        .agents-sidebar {
          width: 320px;
          background-color: white;
          border-right: 1px solid #e2e8f0;
          overflow-y: auto;
        }

        .sidebar-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px;
          border-bottom: 1px solid #e2e8f0;
        }

        .sidebar-header h2 {
          margin: 0;
          color: #1e293b;
          font-size: 18px;
          font-weight: 600;
        }

        .agent-count {
          color: #64748b;
          font-size: 14px;
          background-color: #f1f5f9;
          padding: 2px 8px;
          border-radius: 12px;
        }

        .loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 60px 20px;
          color: #64748b;
        }

        .loading-spinner {
          width: 24px;
          height: 24px;
          border: 2px solid #e2e8f0;
          border-top: 2px solid #3b82f6;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 12px;
        }

        .empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 60px 20px;
          color: #64748b;
          text-align: center;
        }

        .empty-icon {
          font-size: 48px;
          margin-bottom: 16px;
        }

        .empty-state button {
          margin-top: 16px;
          padding: 8px 16px;
          background-color: #3b82f6;
          color: white;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
        }

        .empty-state button:hover {
          background-color: #2563eb;
        }

        .agents-list {
          padding: 12px;
        }

        .agent-item {
          padding: 16px;
          margin-bottom: 8px;
          background-color: #f8fafc;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s;
          border: 2px solid transparent;
        }

        .agent-item:hover {
          background-color: #f1f5f9;
          border-color: #e2e8f0;
        }

        .agent-item.selected {
          background-color: #eff6ff;
          border-color: #3b82f6;
        }

        .agent-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
        }

        .agent-header h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
        }

        .status {
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 11px;
          font-weight: 500;
        }

        .status.active {
          background-color: #dcfce7;
          color: #166534;
        }

        .status.inactive {
          background-color: #f1f5f9;
          color: #64748b;
        }

        .status.error {
          background-color: #fef2f2;
          color: #dc2626;
        }

        .agent-description {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: #64748b;
          line-height: 1.4;
        }

        .agent-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;
          color: #64748b;
          margin-bottom: 8px;
        }

        .agent-type {
          background-color: #f1f5f9;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 11px;
        }

        .agent-capabilities {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
        }

        .capability-tag {
          padding: 2px 6px;
          background-color: #dbeafe;
          color: #1e40af;
          border-radius: 4px;
          font-size: 11px;
          font-weight: 500;
        }

        .more-capabilities {
          padding: 2px 6px;
          background-color: #f1f5f9;
          color: #64748b;
          border-radius: 4px;
          font-size: 11px;
        }

        .agent-actions {
          margin-top: 8px;
          display: flex;
          justify-content: flex-end;
        }

        .delete-btn {
          padding: 4px 8px;
          background-color: #fef2f2;
          color: #dc2626;
          border: none;
          border-radius: 4px;
          font-size: 12px;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .delete-btn:hover {
          background-color: #fee2e2;
        }

        .agent-details {
          flex: 1;
          padding: 24px;
          overflow-y: auto;
        }

        .agent-details-content {
          max-width: 800px;
        }

        .agent-details .agent-header {
          background: none;
          border: none;
          padding: 0;
          margin-bottom: 24px;
        }

        .agent-details .agent-header h2 {
          font-size: 28px;
          margin-bottom: 8px;
        }

        .agent-info-section,
        .capabilities-section,
        .agent-description-section {
          background-color: white;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 16px;
          border: 1px solid #e2e8f0;
        }

        .agent-info-section h3,
        .capabilities-section h3,
        .agent-description-section h3 {
          margin: 0 0 16px 0;
          color: #1e293b;
          font-size: 16px;
          font-weight: 600;
        }

        .info-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 16px;
        }

        .info-item {
          display: flex;
          flex-direction: column;
        }

        .info-item label {
          font-weight: 500;
          color: #374151;
          margin-bottom: 4px;
          font-size: 14px;
        }

        .info-item span {
          color: #6b7280;
          font-size: 14px;
        }

        .capabilities-list {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }

        .no-capabilities {
          color: #64748b;
          font-style: italic;
        }

        .no-selection {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #64748b;
          text-align: center;
        }

        .no-selection-icon {
          font-size: 64px;
          margin-bottom: 16px;
        }

        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 1000;
        }

        .modal {
          background-color: white;
          border-radius: 12px;
          width: 500px;
          max-width: 90vw;
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px;
          border-bottom: 1px solid #e2e8f0;
        }

        .modal-header h2 {
          margin: 0;
          color: #1e293b;
          font-size: 18px;
          font-weight: 600;
        }

        .close-btn {
          background: none;
          border: none;
          font-size: 24px;
          cursor: pointer;
          color: #64748b;
          padding: 0;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          transition: background-color 0.2s;
        }

        .close-btn:hover {
          background-color: #f1f5f9;
        }

        .modal-content {
          padding: 20px;
        }

        .form-group {
          margin-bottom: 20px;
        }

        .form-group label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          color: #374151;
          font-size: 14px;
        }

        .form-group input,
        .form-group select {
          width: 100%;
          padding: 10px 12px;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 14px;
          transition: border-color 0.2s;
        }

        .form-group input:focus,
        .form-group select:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .capabilities-preview {
          margin-top: 8px;
          font-size: 12px;
          color: #64748b;
        }

        .modal-footer {
          display: flex;
          justify-content: flex-end;
          gap: 12px;
          padding: 20px;
          border-top: 1px solid #e2e8f0;
        }

        .cancel-btn,
        .create-btn {
          padding: 10px 20px;
          border: none;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .cancel-btn {
          background-color: #f1f5f9;
          color: #64748b;
        }

        .cancel-btn:hover {
          background-color: #e2e8f0;
        }

        .create-btn {
          background-color: #3b82f6;
          color: white;
        }

        .create-btn:hover {
          background-color: #2563eb;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default AgentWorkspace;
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { Agent, Workflow, Document, Settings } from '../types/api';
import apiService from '../services/apiService';

// 应用状态接口
interface AppState {
  // 用户状态
  user: {
    id: string;
    name: string;
    email: string;
    tenantId: string;
    isAuthenticated: boolean;
  } | null;
  
  // 智能体状态
  agents: {
    items: Agent[];
    selectedAgent: Agent | null;
    loading: boolean;
    error: string | null;
  };
  
  // 工作流状态
  workflows: {
    items: Workflow[];
    selectedWorkflow: Workflow | null;
    loading: boolean;
    error: string | null;
  };
  
  // 知识库状态
  knowledge: {
    items: Document[];
    selectedDocument: Document | null;
    loading: boolean;
    error: string | null;
  };
  
  // 设置状态
  settings: Settings | null;
  
  // UI 状态
  ui: {
    loading: boolean;
    error: string | null;
    successMessage: string | null;
    sidebarOpen: boolean;
  };
  
  // Actions
  // 用户 Actions
  setUser: (user: AppState['user']) => void;
  clearUser: () => void;
  
  // 智能体 Actions
  fetchAgents: () => Promise<void>;
  createAgent: (agentData: Partial<Agent>) => Promise<void>;
  updateAgent: (id: string, agentData: Partial<Agent>) => Promise<void>;
  deleteAgent: (id: string) => Promise<void>;
  selectAgent: (agent: Agent | null) => void;
  clearAgentsError: () => void;
  
  // 工作流 Actions
  fetchWorkflows: () => Promise<void>;
  createWorkflow: (workflowData: Partial<Workflow>) => Promise<void>;
  updateWorkflow: (id: string, workflowData: Partial<Workflow>) => Promise<void>;
  deleteWorkflow: (id: string) => Promise<void>;
  selectWorkflow: (workflow: Workflow | null) => void;
  clearWorkflowsError: () => void;
  
  // 知识库 Actions
  fetchKnowledge: () => Promise<void>;
  createDocument: (documentData: Partial<Document>) => Promise<void>;
  updateDocument: (id: string, documentData: Partial<Document>) => Promise<void>;
  deleteDocument: (id: string) => Promise<void>;
  selectDocument: (document: Document | null) => void;
  clearKnowledgeError: () => void;
  
  // 设置 Actions
  fetchSettings: () => Promise<void>;
  updateSettings: (settings: Partial<Settings>) => Promise<void>;
  
  // UI Actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setSuccessMessage: (message: string | null) => void;
  toggleSidebar: () => void;
  clearUIState: () => void;
}

// 创建 Zustand store
export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        user: null,
        agents: {
          items: [],
          selectedAgent: null,
          loading: false,
          error: null,
        },
        workflows: {
          items: [],
          selectedWorkflow: null,
          loading: false,
          error: null,
        },
        knowledge: {
          items: [],
          selectedDocument: null,
          loading: false,
          error: null,
        },
        settings: null,
        ui: {
          loading: false,
          error: null,
          successMessage: null,
          sidebarOpen: true,
        },

        // 用户 Actions
        setUser: (user) => set({ user }),
        clearUser: () => set({ user: null }),

        // 智能体 Actions
        fetchAgents: async () => {
          set((state) => ({ 
            agents: { ...state.agents, loading: true, error: null } 
          }));
          
          try {
            const response = await apiService.get<{items: Agent[], total: number}>('/agents');
            set((state) => ({
              agents: {
                ...state.agents,
                items: response.data.items || [],
                loading: false,
                error: null,
              },
            }));
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '获取智能体列表失败';
            set((state) => ({
              agents: {
                ...state.agents,
                loading: false,
                error: errorMessage,
              },
            }));
            throw error;
          }
        },

        createAgent: async (agentData) => {
          set((state) => ({ 
            agents: { ...state.agents, loading: true, error: null } 
          }));
          
          try {
            const response = await apiService.post<Agent>('/agents/', agentData);
            set((state) => ({
              agents: {
                ...state.agents,
                items: [...state.agents.items, response.data],
                loading: false,
                error: null,
              },
            }));
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '创建智能体失败';
            set((state) => ({
              agents: {
                ...state.agents,
                loading: false,
                error: errorMessage,
              },
            }));
            throw error;
          }
        },

        updateAgent: async (id, agentData) => {
          set((state) => ({ 
            agents: { ...state.agents, loading: true, error: null } 
          }));
          
          try {
            const response = await apiService.put<Agent>(`/agents/${id}`, agentData);
            set((state) => ({
              agents: {
                ...state.agents,
                items: state.agents.items.map(agent => 
                  agent.id === id ? response.data : agent
                ),
                loading: false,
                error: null,
              },
            }));
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '更新智能体失败';
            set((state) => ({
              agents: {
                ...state.agents,
                loading: false,
                error: errorMessage,
              },
            }));
            throw error;
          }
        },

        deleteAgent: async (id) => {
          set((state) => ({ 
            agents: { ...state.agents, loading: true, error: null } 
          }));
          
          try {
            await apiService.delete(`/agents/${id}`);
            set((state) => ({
              agents: {
                ...state.agents,
                items: state.agents.items.filter(agent => agent.id !== id),
                loading: false,
                error: null,
              },
            }));
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '删除智能体失败';
            set((state) => ({
              agents: {
                ...state.agents,
                loading: false,
                error: errorMessage,
              },
            }));
            throw error;
          }
        },

        selectAgent: (agent) => set((state) => ({
          agents: { ...state.agents, selectedAgent: agent }
        })),

        clearAgentsError: () => set((state) => ({
          agents: { ...state.agents, error: null }
        })),

        // 工作流 Actions
        fetchWorkflows: async () => {
          set((state) => ({ 
            workflows: { ...state.workflows, loading: true, error: null } 
          }));
          
          try {
            const response = await apiService.get<Workflow[]>('/workflows');
            set((state) => ({
              workflows: {
                ...state.workflows,
                items: response.data,
                loading: false,
                error: null,
              },
            }));
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '获取工作流列表失败';
            set((state) => ({
              workflows: {
                ...state.workflows,
                loading: false,
                error: errorMessage,
              },
            }));
            throw error;
          }
        },

        createWorkflow: async (workflowData) => {
          set((state) => ({ 
            workflows: { ...state.workflows, loading: true, error: null } 
          }));
          
          try {
            const response = await apiService.post<Workflow>('/workflows', workflowData);
            set((state) => ({
              workflows: {
                ...state.workflows,
                items: [...state.workflows.items, response.data],
                loading: false,
                error: null,
              },
            }));
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '创建工作流失败';
            set((state) => ({
              workflows: {
                ...state.workflows,
                loading: false,
                error: errorMessage,
              },
            }));
            throw error;
          }
        },

        updateWorkflow: async (id, workflowData) => {
          set((state) => ({ 
            workflows: { ...state.workflows, loading: true, error: null } 
          }));
          
          try {
            const response = await apiService.put<Workflow>(`/workflows/${id}`, workflowData);
            set((state) => ({
              workflows: {
                ...state.workflows,
                items: state.workflows.items.map(workflow => 
                  workflow.id === id ? response.data : workflow
                ),
                loading: false,
                error: null,
              },
            }));
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '更新工作流失败';
            set((state) => ({
              workflows: {
                ...state.workflows,
                loading: false,
                error: errorMessage,
              },
            }));
            throw error;
          }
        },

        deleteWorkflow: async (id) => {
          set((state) => ({ 
            workflows: { ...state.workflows, loading: true, error: null } 
          }));
          
          try {
            await apiService.delete(`/workflows/${id}`);
            set((state) => ({
              workflows: {
                ...state.workflows,
                items: state.workflows.items.filter(workflow => workflow.id !== id),
                loading: false,
                error: null,
              },
            }));
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '删除工作流失败';
            set((state) => ({
              workflows: {
                ...state.workflows,
                loading: false,
                error: errorMessage,
              },
            }));
            throw error;
          }
        },

        selectWorkflow: (workflow) => set((state) => ({
          workflows: { ...state.workflows, selectedWorkflow: workflow }
        })),

        clearWorkflowsError: () => set((state) => ({
          workflows: { ...state.workflows, error: null }
        })),

        // 知识库 Actions
        fetchKnowledge: async () => {
          set((state) => ({ 
            knowledge: { ...state.knowledge, loading: true, error: null } 
          }));
          
          try {
            const response = await apiService.get<Document[]>('/knowledge');
            set((state) => ({
              knowledge: {
                ...state.knowledge,
                items: response.data,
                loading: false,
                error: null,
              },
            }));
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '获取知识库失败';
            set((state) => ({
              knowledge: {
                ...state.knowledge,
                loading: false,
                error: errorMessage,
              },
            }));
            throw error;
          }
        },

        createDocument: async (documentData) => {
          set((state) => ({ 
            knowledge: { ...state.knowledge, loading: true, error: null } 
          }));
          
          try {
            const response = await apiService.post<Document>('/knowledge', documentData);
            set((state) => ({
              knowledge: {
                ...state.knowledge,
                items: [...state.knowledge.items, response.data],
                loading: false,
                error: null,
              },
            }));
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '创建文档失败';
            set((state) => ({
              knowledge: {
                ...state.knowledge,
                loading: false,
                error: errorMessage,
              },
            }));
            throw error;
          }
        },

        updateDocument: async (id, documentData) => {
          set((state) => ({ 
            knowledge: { ...state.knowledge, loading: true, error: null } 
          }));
          
          try {
            const response = await apiService.put<Document>(`/knowledge/${id}`, documentData);
            set((state) => ({
              knowledge: {
                ...state.knowledge,
                items: state.knowledge.items.map(doc => 
                  doc.id === id ? response.data : doc
                ),
                loading: false,
                error: null,
              },
            }));
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '更新文档失败';
            set((state) => ({
              knowledge: {
                ...state.knowledge,
                loading: false,
                error: errorMessage,
              },
            }));
            throw error;
          }
        },

        deleteDocument: async (id) => {
          set((state) => ({ 
            knowledge: { ...state.knowledge, loading: true, error: null } 
          }));
          
          try {
            await apiService.delete(`/knowledge/${id}`);
            set((state) => ({
              knowledge: {
                ...state.knowledge,
                items: state.knowledge.items.filter(doc => doc.id !== id),
                loading: false,
                error: null,
              },
            }));
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '删除文档失败';
            set((state) => ({
              knowledge: {
                ...state.knowledge,
                loading: false,
                error: errorMessage,
              },
            }));
            throw error;
          }
        },

        selectDocument: (document) => set((state) => ({
          knowledge: { ...state.knowledge, selectedDocument: document }
        })),

        clearKnowledgeError: () => set((state) => ({
          knowledge: { ...state.knowledge, error: null }
        })),

        // 设置 Actions
        fetchSettings: async () => {
          try {
            const response = await apiService.get<Settings>('/settings');
            set({ settings: response.data });
          } catch (error) {
            console.error('获取设置失败:', error);
          }
        },

        updateSettings: async (settingsData) => {
          try {
            const response = await apiService.put<Settings>('/settings', settingsData);
            set({ settings: response.data });
          } catch (error) {
            console.error('更新设置失败:', error);
            throw error;
          }
        },

        // UI Actions
        setLoading: (loading) => set((state) => ({
          ui: { ...state.ui, loading }
        })),

        setError: (error) => set((state) => ({
          ui: { ...state.ui, error }
        })),

        setSuccessMessage: (message) => set((state) => ({
          ui: { ...state.ui, successMessage: message }
        })),

        toggleSidebar: () => set((state) => ({
          ui: { ...state.ui, sidebarOpen: !state.ui.sidebarOpen }
        })),

        clearUIState: () => set((state) => ({
          ui: {
            ...state.ui,
            loading: false,
            error: null,
            successMessage: null,
          }
        })),
      }),
      {
        name: 'sgaspace-storage',
        partialize: (state) => ({
          user: state.user,
          settings: state.settings,
          ui: {
            sidebarOpen: state.ui.sidebarOpen,
          },
        }),
      }
    ),
    {
      name: 'sgaspace-store',
    }
  )
);

// 导出 hooks
export const useAgents = () => {
  const store = useAppStore();
  return {
    agents: store.agents.items,
    selectedAgent: store.agents.selectedAgent,
    loading: store.agents.loading,
    error: store.agents.error,
    fetchAgents: store.fetchAgents,
    createAgent: store.createAgent,
    updateAgent: store.updateAgent,
    deleteAgent: store.deleteAgent,
    selectAgent: store.selectAgent,
    clearError: store.clearAgentsError,
  };
};

export const useWorkflows = () => {
  const store = useAppStore();
  return {
    workflows: store.workflows.items,
    selectedWorkflow: store.workflows.selectedWorkflow,
    loading: store.workflows.loading,
    error: store.workflows.error,
    fetchWorkflows: store.fetchWorkflows,
    createWorkflow: store.createWorkflow,
    updateWorkflow: store.updateWorkflow,
    deleteWorkflow: store.deleteWorkflow,
    selectWorkflow: store.selectWorkflow,
    clearError: store.clearWorkflowsError,
  };
};

export const useKnowledge = () => {
  const store = useAppStore();
  return {
    documents: store.knowledge.items,
    selectedDocument: store.knowledge.selectedDocument,
    loading: store.knowledge.loading,
    error: store.knowledge.error,
    fetchKnowledge: store.fetchKnowledge,
    createDocument: store.createDocument,
    updateDocument: store.updateDocument,
    deleteDocument: store.deleteDocument,
    selectDocument: store.selectDocument,
    clearError: store.clearKnowledgeError,
  };
};

export const useSettings = () => {
  const store = useAppStore();
  return {
    settings: store.settings,
    fetchSettings: store.fetchSettings,
    updateSettings: store.updateSettings,
  };
};

export const useUI = () => {
  const store = useAppStore();
  return {
    loading: store.ui.loading,
    error: store.ui.error,
    successMessage: store.ui.successMessage,
    sidebarOpen: store.ui.sidebarOpen,
    setLoading: store.setLoading,
    setError: store.setError,
    setSuccessMessage: store.setSuccessMessage,
    toggleSidebar: store.toggleSidebar,
    clearUIState: store.clearUIState,
  };
};

export const useUser = () => {
  const store = useAppStore();
  return {
    user: store.user,
    setUser: store.setUser,
    clearUser: store.clearUser,
  };
};
{"name": "sgaspace-desktop", "version": "1.0.0", "description": "SGASpace Desktop Application", "main": "dist/main.js", "scripts": {"dev": "concurrently \"npm:dev:renderer\" \"npm:dev:main\"", "dev:renderer": "vite", "dev:main": "echo '🚀 Starting main process build...' && npm run build:main && echo '⏳ Waiting for file system...' && sleep 1 && echo '⚡️ Launching Electron...' && electron dist/electron/main.js --dev", "electron": "npm run build:main && electron dist/electron/main.js", "build": "npm run build:main && npm run build:renderer", "build:main": "rm -rf dist && tsc --pretty -p tsconfig.main.json && echo '✅ Main process built successfully to dist/main.js'", "build:renderer": "vite build", "package": "electron-forge package", "make": "electron-forge make", "start": "electron-forge start", "lint": "eslint . --ext .ts,.tsx --max-warnings 0", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "typecheck": "tsc --noEmit && tsc --project tsconfig.main.json --noEmit && tsc --project tsconfig.renderer.json --noEmit", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "prepare": "echo 'Skipping husky install'", "pre-commit": "lint-staged"}, "keywords": ["electron", "desktop", "ai", "workspace"], "author": "SGASpace Team", "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.2.0", "@electron-forge/maker-deb": "^7.2.0", "@electron-forge/maker-rpm": "^7.2.0", "@electron-forge/maker-squirrel": "^7.2.0", "@electron-forge/maker-zip": "^7.2.0", "@electron-forge/plugin-auto-unpack-natives": "^7.2.0", "@electron-forge/plugin-fuses": "^7.2.0", "@electron-toolkit/utils": "^4.0.0", "@electron/fuses": "^1.6.0", "@types/better-sqlite3": "^7.6.13", "@types/node": "^20.19.10", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "@vitejs/plugin-react": "^4.7.0", "@vitest/coverage-v8": "^1.0.0", "concurrently": "^8.2.0", "electron": "^28.1.0", "electron-rebuild": "^3.2.9", "eslint": "^8.54.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "lint-staged": "^15.0.0", "prettier": "^3.0.0", "typescript": "^5.9.2", "vite": "^5.0.0", "vitest": "^1.0.0"}, "dependencies": {"@electron/remote": "^2.1.0", "@types/uuid": "^10.0.0", "better-sqlite3": "^12.2.0", "electron-store": "^8.1.0", "electron-updater": "^6.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "uuid": "^11.1.0", "zod": "^3.22.4", "zustand": "^4.4.0"}}
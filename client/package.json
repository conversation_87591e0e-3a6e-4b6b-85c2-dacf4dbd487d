{"name": "sgaspace-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@sgaspace/hooks": "*", "@sgaspace/types": "*", "@sgaspace/ui-components": "*", "@testing-library/dom": "^10.4.1", "autoprefixer": "^10.4.20", "axios": "^1.8.2", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "framer-motion": "^12.5.0", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.1.0", "next-themes": "^0.4.4", "react": "^19", "react-chartjs-2": "^5.3.0", "react-day-picker": "8.10.1", "react-dom": "^19", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.1", "react-hot-toast": "^2.5.2", "react-resizable-panels": "^2.1.7", "reactflow": "^11.11.4", "recharts": "2.15.0", "socket.io-client": "^4.8.1", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.16", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "jsdom": "^26.0.0", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5", "vitest": "^3.1.1"}}
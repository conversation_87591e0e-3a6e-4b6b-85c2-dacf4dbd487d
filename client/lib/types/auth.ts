/**
 * 认证相关类型定义
 */

// 用户角色
export type UserRole = 
  | 'admin'      // 管理员
  | 'developer'  // 开发者
  | 'user'       // 普通用户
  | 'guest'      // 访客

// 用户状态
export type UserStatus = 
  | 'active'    // 活跃
  | 'inactive'  // 非活跃
  | 'suspended' // 已暂停
  | 'deleted'   // 已删除

// 用户信息
export interface User {
  id: string;
  username: string;
  email: string;
  name?: string;
  role: UserRole;
  status: UserStatus | 'pending';
  tenantId: string;
  currentTenant?: {
    id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  preferences?: {
    theme?: 'light' | 'dark' | 'system';
    language?: string;
    timezone?: string;
    [key: string]: any;
  };
}

// 认证状态
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// 登录请求
export interface LoginRequest {
  username: string;
  password: string;
  remember?: boolean;
}

// 登录响应
export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
}

// 注册请求
export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// 重置密码请求
export interface ResetPasswordRequest {
  email: string;
  token: string;
  newPassword: string;
  confirmPassword: string;
}

// 更新用户请求
export interface UpdateUserRequest {
  username?: string;
  email?: string;
  currentPassword?: string;
  newPassword?: string;
  preferences?: Record<string, any>;
} 
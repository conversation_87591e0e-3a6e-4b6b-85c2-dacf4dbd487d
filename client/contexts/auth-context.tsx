'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { apiClient } from '@/lib/api-client'
import { API_ROUTES } from '@/lib/api-config'
import { User, UserRole, UserStatus } from '@/lib/types/auth'

interface AuthContextType {
  user: User | null
  loading: boolean
  isLoading: boolean
  isAuthenticated: boolean
  error: string | null
  token: string | null
  login: (email: string, password: string) => Promise<void>
  register: (email: string, password: string, username: string) => Promise<void>
  logout: () => void
  checkAuth: () => Promise<void>
  clearError: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [token, setToken] = useState<string | null>(null)

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        setUser(null)
        return
      }

      const data = await apiClient.get(`${API_ROUTES.AUTH.ME}`)
      
      // 确保用户数据符合User类型定义
      const userData: User = {
        id: data.id || '1',
        username: data.username || 'admin',
        email: data.email || '<EMAIL>',
        role: data.role || 'admin',
        status: data.status || 'active',
        tenantId: data.tenantId || 'default',
        createdAt: data.createdAt || new Date().toISOString(),
        updatedAt: data.updatedAt || new Date().toISOString(),
        lastLoginAt: data.lastLoginAt,
        preferences: data.preferences
      }
      
      setUser(userData)
    } catch (error) {
      console.error('验证用户状态失败:', error)
      
      // 开发环境下提供模拟用户数据
      if (process.env.NODE_ENV === 'development') {
        const mockUser: User = {
          id: '1',
          username: 'admin',
          email: '<EMAIL>',
          role: 'admin',
          status: 'active',
          tenantId: 'default',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
        setUser(mockUser)
      } else {
      setUser(null)
      localStorage.removeItem('auth_token')
      }
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    checkAuth()
  }, [])

  const login = async (email: string, password: string) => {
    try {
      setError(null)
      const { token, user } = await apiClient.post(API_ROUTES.AUTH.LOGIN, {
        email,
        password,
      })

      apiClient.setToken(token)
      setToken(token)
      setUser(user)
      router.push('/dashboard')
    } catch (error) {
      setError(error instanceof Error ? error.message : '登录失败')
      throw error
    }
  }

  const register = async (email: string, password: string, username: string) => {
    try {
      setError(null)
      const { token, user } = await apiClient.post(API_ROUTES.AUTH.REGISTER, {
        email,
        password,
        username,
      })

      apiClient.setToken(token)
      setToken(token)
      setUser(user)
      router.push('/dashboard')
    } catch (error) {
      setError(error instanceof Error ? error.message : '注册失败')
      throw error
    }
  }

  const clearError = () => {
    setError(null)
  }

  const logout = () => {
    apiClient.clearToken()
    setUser(null)
    setToken(null)
    router.push('/auth/login')
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        isLoading: loading,
        isAuthenticated: !!user,
        error,
        token,
        login,
        register,
        logout,
        checkAuth,
        clearError,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
} 
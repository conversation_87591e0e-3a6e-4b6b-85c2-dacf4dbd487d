"use client";

import React, { ReactNode, useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { Loader2 } from "lucide-react";

// 公开路由列表，不需要认证
const PUBLIC_ROUTES = [
  "/auth/login",
  "/auth/register",
  "/auth/verify-email",
  "/auth/forgot-password",
  "/auth/reset-password",
  "/auth/register-success",
];

interface AuthGuardProps {
  children: ReactNode;
}

export function AuthGuard({ children }: AuthGuardProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  const isPublicRoute = PUBLIC_ROUTES.includes(pathname);

  useEffect(() => {
    // 如果用户未登录且不是公开路由，重定向到登录页面
    if (!isLoading && !isAuthenticated && !isPublicRoute) {
      router.push("/auth/login");
    }

    // 如果用户已登录且访问的是登录/注册页面，重定向到仪表盘
    if (!isLoading && isAuthenticated && isPublicRoute) {
      router.push("/dashboard");
    }
  }, [isAuthenticated, isLoading, isPublicRoute, router]);

  // 如果正在加载，显示加载指示器
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
      </div>
    );
  }

  // 如果是公开路由或用户已认证，显示子组件
  if (isPublicRoute || isAuthenticated) {
    return <>{children}</>;
  }

  // 默认情况下，显示空白页面（等待重定向完成）
  return (
    <div className="flex items-center justify-center min-h-screen">
      <Loader2 className="w-8 h-8 animate-spin text-primary" />
    </div>
  );
} 
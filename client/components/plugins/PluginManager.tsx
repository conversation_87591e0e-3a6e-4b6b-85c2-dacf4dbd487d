// 简化的插件管理器组件
export function PluginManager() {
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">插件管理</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="p-4 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">已安装插件</h3>
          <p className="text-sm text-gray-600">暂无已安装的插件</p>
        </div>
        <div className="p-4 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">可用插件</h3>
          <p className="text-sm text-gray-600">浏览插件商店</p>
        </div>
        <div className="p-4 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">插件设置</h3>
          <p className="text-sm text-gray-600">配置插件参数</p>
        </div>
      </div>
    </div>
  )
}
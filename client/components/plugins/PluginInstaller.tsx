// Plugin installer component
export function PluginInstaller({ 
  onInstall, 
  onCancel 
}: { 
  onInstall: (pluginId: string, version: string, source: string, config: any) => Promise<void>;
  onCancel?: () => void;
}) {
  return (
    <div className="p-4 border rounded">
      <h3 className="text-lg font-semibold mb-2">插件安装器</h3>
      <p className="text-sm text-gray-600">插件安装功能开发中</p>
    </div>
  )
}
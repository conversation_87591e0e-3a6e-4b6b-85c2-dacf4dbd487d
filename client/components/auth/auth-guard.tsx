"use client";

import React, { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";

interface AuthGuardProps {
  children: React.ReactNode;
}

/**
 * 身份验证守卫组件
 * 
 * 用于保护需要认证的路由，未登录用户会被重定向到登录页面
 */
export function AuthGuard({ children }: AuthGuardProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // 如果用户未认证且不在认证相关路径，则重定向到登录页面
    if (!isLoading && !isAuthenticated && !pathname.startsWith("/auth/")) {
      router.push("/auth/login");
    }
  }, [isAuthenticated, isLoading, pathname, router]);

  // 如果正在加载中，显示加载指示器
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // 如果用户已认证或在认证相关页面，则渲染子组件
  if (isAuthenticated || pathname.startsWith("/auth/")) {
    return <>{children}</>;
  }

  // 默认返回null，等待重定向
  return null;
}

/**
 * 公共路由守卫组件
 * 
 * 用于处理公共路由，如果用户已登录则重定向到仪表盘
 */
export function PublicRouteGuard({ children }: AuthGuardProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // 如果用户已认证且在认证相关路径，则重定向到仪表盘
    if (!isLoading && isAuthenticated && pathname.startsWith("/auth/")) {
      router.push("/dashboard");
    }
  }, [isAuthenticated, isLoading, pathname, router]);

  // 如果正在加载中，显示加载指示器
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return <>{children}</>;
} 
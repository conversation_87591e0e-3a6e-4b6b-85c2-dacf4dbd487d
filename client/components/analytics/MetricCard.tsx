// Metric card component
export function MetricCard({ 
  title, 
  value, 
  change, 
  trend, 
  icon, 
  color, 
  format 
}: { 
  title: string; 
  value: string | number;
  change?: any;
  trend?: any;
  icon?: any;
  color?: string;
  format?: string;
}) {
  return (
    <div className="p-4 border rounded">
      <h3 className="text-sm font-medium">{title}</h3>
      <p className="text-2xl font-bold">{value}</p>
    </div>
  )
}
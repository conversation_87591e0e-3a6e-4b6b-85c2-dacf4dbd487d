'use client';

import React, { useState } from 'react';
import { 
  <PERSON>, 
  Download, 
  Co<PERSON>, 
  Share2, 
  Star, 
  Clock, 
  Zap, 
  DollarSign,
  ChevronDown,
  ChevronUp,
  Image,
  FileText,
  Music,
  Video
} from 'lucide-react';

interface ProcessingResult {
  task_id: string;
  status: string;
  result: any;
  confidence?: number;
  model_name?: string;
  processing_time?: number;
  error?: string;
  metadata?: {
    filename?: string;
    content_type?: string;
    size?: number;
  };
}

interface MultimodalResultViewerProps {
  results: ProcessingResult[];
  onResultSelect?: (result: ProcessingResult) => void;
  className?: string;
}

const MultimodalResultViewer: React.FC<MultimodalResultViewerProps> = ({
  results,
  onResultSelect,
  className = ''
}) => {
  const [expandedResults, setExpandedResults] = useState<Set<string>>(new Set());
  const [selectedResult, setSelectedResult] = useState<ProcessingResult | null>(null);

  // 切换结果展开状态
  const toggleExpanded = (taskId: string) => {
    const newExpanded = new Set(expandedResults);
    if (newExpanded.has(taskId)) {
      newExpanded.delete(taskId);
    } else {
      newExpanded.add(taskId);
    }
    setExpandedResults(newExpanded);
  };

  // 复制结果到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // 这里可以添加成功提示
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  // 下载结果
  const downloadResult = (result: ProcessingResult) => {
    const content = typeof result.result === 'string' ? result.result : JSON.stringify(result.result, null, 2);
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `result_${result.task_id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 获取文件类型图标
  const getFileTypeIcon = (contentType?: string) => {
    if (!contentType) return <FileText className="w-5 h-5" />;
    
    if (contentType.startsWith('image/')) return <Image className="w-5 h-5" />;
    if (contentType.startsWith('audio/')) return <Music className="w-5 h-5" />;
    if (contentType.startsWith('video/')) return <Video className="w-5 h-5" />;
    return <FileText className="w-5 h-5" />;
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'processing': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // 格式化置信度
  const formatConfidence = (confidence?: number) => {
    if (confidence === undefined) return 'N/A';
    return `${(confidence * 100).toFixed(1)}%`;
  };

  // 格式化处理时间
  const formatProcessingTime = (time?: number) => {
    if (time === undefined) return 'N/A';
    return `${time.toFixed(2)}s`;
  };

  // 渲染结果内容
  const renderResultContent = (result: any) => {
    if (typeof result === 'string') {
      return (
        <div className="prose max-w-none">
          <p className="whitespace-pre-wrap">{result}</p>
        </div>
      );
    }

    if (typeof result === 'object' && result !== null) {
      // 如果是带时间戳的转录结果
      if (result.text && result.segments) {
        return (
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">转录文本：</h4>
              <p className="whitespace-pre-wrap bg-gray-50 p-3 rounded">{result.text}</p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">时间戳片段：</h4>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {result.segments.map((segment: any, index: number) => (
                  <div key={index} className="flex items-start space-x-3 text-sm">
                    <span className="text-gray-500 font-mono">
                      {segment.start.toFixed(1)}s - {segment.end.toFixed(1)}s
                    </span>
                    <span className="flex-1">{segment.text}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
      }

      // 其他对象类型
      return (
        <pre className="bg-gray-50 p-3 rounded text-sm overflow-x-auto">
          {JSON.stringify(result, null, 2)}
        </pre>
      );
    }

    return <p className="text-gray-500">无法显示结果内容</p>;
  };

  if (results.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="text-gray-400 mb-4">
          <Eye className="w-16 h-16 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">暂无处理结果</h3>
        <p className="text-gray-500">上传文件并处理后，结果将在这里显示</p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 结果统计 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{results.length}</div>
            <div className="text-sm text-gray-600">总结果数</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {results.filter(r => r.status === 'completed').length}
            </div>
            <div className="text-sm text-gray-600">成功</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {results.filter(r => r.status === 'failed').length}
            </div>
            <div className="text-sm text-gray-600">失败</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {results.filter(r => r.confidence && r.confidence > 0.8).length}
            </div>
            <div className="text-sm text-gray-600">高置信度</div>
          </div>
        </div>
      </div>

      {/* 结果列表 */}
      <div className="space-y-4">
        {results.map((result) => (
          <div
            key={result.task_id}
            className="bg-white rounded-lg border border-gray-200 overflow-hidden"
          >
            {/* 结果头部 */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getFileTypeIcon(result.metadata?.content_type)}
                  <div>
                    <h3 className="font-medium text-gray-900">
                      {result.metadata?.filename || `任务 ${result.task_id.slice(0, 8)}`}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(result.status)}`}>
                        {result.status === 'completed' ? '已完成' : 
                         result.status === 'failed' ? '失败' : 
                         result.status === 'processing' ? '处理中' : result.status}
                      </span>
                      {result.model_name && (
                        <span className="flex items-center">
                          <Zap className="w-3 h-3 mr-1" />
                          {result.model_name}
                        </span>
                      )}
                      {result.processing_time && (
                        <span className="flex items-center">
                          <Clock className="w-3 h-3 mr-1" />
                          {formatProcessingTime(result.processing_time)}
                        </span>
                      )}
                      {result.confidence && (
                        <span className="flex items-center">
                          <Star className="w-3 h-3 mr-1" />
                          {formatConfidence(result.confidence)}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {/* 操作按钮 */}
                  {result.status === 'completed' && (
                    <>
                      <button
                        onClick={() => copyToClipboard(typeof result.result === 'string' ? result.result : JSON.stringify(result.result))}
                        className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                        title="复制结果"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => downloadResult(result)}
                        className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                        title="下载结果"
                      >
                        <Download className="w-4 h-4" />
                      </button>
                    </>
                  )}
                  
                  {/* 展开/收起按钮 */}
                  <button
                    onClick={() => toggleExpanded(result.task_id)}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                  >
                    {expandedResults.has(result.task_id) ? (
                      <ChevronUp className="w-4 h-4" />
                    ) : (
                      <ChevronDown className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* 结果内容 */}
            {expandedResults.has(result.task_id) && (
              <div className="p-4">
                {result.status === 'completed' && result.result ? (
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">处理结果：</h4>
                      {renderResultContent(result.result)}
                    </div>
                  </div>
                ) : result.status === 'failed' && result.error ? (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h4 className="font-medium text-red-800 mb-2">错误信息：</h4>
                    <p className="text-red-700">{result.error}</p>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <div className="animate-spin w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2"></div>
                    <p>处理中...</p>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 详细结果模态框 */}
      {selectedResult && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold text-gray-900">
                  处理结果详情
                </h2>
                <button
                  onClick={() => setSelectedResult(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <span className="w-6 h-6">×</span>
                </button>
              </div>
            </div>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              {renderResultContent(selectedResult.result)}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MultimodalResultViewer;
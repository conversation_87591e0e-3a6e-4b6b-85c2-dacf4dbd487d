'use client';

// 检查路由守卫逻辑，是否正确识别认证状态 
import { useEffect, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';

// 认证路由列表（不需要认证但已认证用户会被重定向）
const authRoutes = [
  '/login',
  '/register',
  '/verify-email',
  '/forgot-password',
  '/reset-password',
  '/register-success',
  '/auth/login',
  '/auth/register',
  '/auth/verify-email',
  '/auth/forgot-password',
  '/auth/reset-password',
  '/auth/register-success',
];

// 公开路由列表（不需要认证）
const publicRoutes = [
  '/',
  '/about',
  '/contact',
  '/privacy-policy',
  '/terms-of-service',
  '/admin',
  '/agent',
  '/agent/chat',
  '/agents',
  '/multi-agent'
];

const RouteGuard: React.FC = () => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [lastCheckedPath, setLastCheckedPath] = useState<string | null>(null);

  useEffect(() => {
    // 开发环境暂时禁用路由守卫重定向
    const isDevelopment = process.env.NODE_ENV === 'development';
    if (isDevelopment) {
      console.log('开发环境：路由守卫重定向已禁用');
      return;
    }
    
    // 避免重复检查相同路径
    if (pathname === lastCheckedPath) return;
    setLastCheckedPath(pathname);
    
    // 输出详细的调试信息
    console.log('路由守卫检查状态:', { 
      isAuthenticated, 
      isLoading, 
      pathname,
      user: user ? `已登录(${user.email})` : '未登录',
      token: localStorage.getItem('accessToken') ? '存在' : '不存在'
    });
    
    // 等待认证状态加载完成
    if (isLoading) {
      console.log('认证状态加载中，暂不处理路由...');
      return;
    }
    
    // 判断是否在认证页面
    const isAuthPage = authRoutes.some(route => pathname.startsWith(route) || pathname === route);
    
    // 修改公共页面匹配逻辑，特别处理聊天页面
    const isPublicPage = publicRoutes.some(route => 
      pathname.startsWith(route) || 
      pathname === route || 
      (route === '/admin' && pathname.startsWith('/admin'))
    ) || pathname.match(/^\/agent\/chat\/[\w-]+$/); // 匹配聊天会话页面
    
    console.log('路径分析:', {
      isAuthPage,
      isPublicPage,
      needsAuth: !isAuthPage && !isPublicPage,
      isSessionPath: !!pathname.match(/^\/agent\/chat\/[\w-]+$/),
      pathname
    });
    
    if (isAuthenticated && isAuthPage) {
      console.log('已认证用户访问认证页面，重定向到dashboard');
      router.push('/dashboard');
    } else if (!isAuthenticated && !isAuthPage && !isPublicPage) {
      console.log('未认证用户访问受保护页面，重定向到登录页');
      // 保存当前URL，以便登录后返回
      if (typeof window !== 'undefined') {
        sessionStorage.setItem('redirectAfterLogin', pathname);
      }
      router.push('/auth/login');
    } else if (isAuthenticated && pathname === '/dashboard') {
      // 检查是否有登录后重定向
      const redirectPath = sessionStorage.getItem('redirectAfterLogin');
      if (redirectPath && redirectPath !== '/dashboard') {
        console.log('登录后重定向到:', redirectPath);
        sessionStorage.removeItem('redirectAfterLogin');
        router.push(redirectPath);
      }
    }
  }, [isAuthenticated, isLoading, pathname, router, user, lastCheckedPath]);

  return null;
};

export default RouteGuard; 
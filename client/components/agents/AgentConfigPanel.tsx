"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { AlertCircle, Plus, X, Settings, Brain, MessageSquare, Zap } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface AgentConfig {
  id?: string;
  name: string;
  description: string;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  capabilities: string[];
  isActive: boolean;
  tags: string[];
  metadata: Record<string, any>;
}

interface AgentConfigPanelProps {
  agent?: AgentConfig;
  onSave: (config: AgentConfig) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const DEFAULT_CAPABILITIES = [
  'text-generation',
  'code-analysis',
  'data-processing',
  'conversation',
  'reasoning',
  'planning',
  'research',
  'translation'
];

const AVAILABLE_MODELS = [
  { value: 'gpt-4', label: 'GPT-4' },
  { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
  { value: 'claude-3-opus', label: 'Claude 3 Opus' },
  { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet' },
  { value: 'gemini-pro', label: 'Gemini Pro' }
];

export function AgentConfigPanel({ agent, onSave, onCancel, isLoading = false }: AgentConfigPanelProps) {
  const [config, setConfig] = useState<AgentConfig>({
    name: '',
    description: '',
    model: 'gpt-4',
    temperature: 0.7,
    maxTokens: 2048,
    systemPrompt: '',
    capabilities: [],
    isActive: true,
    tags: [],
    metadata: {}
  });

  const [newCapability, setNewCapability] = useState('');
  const [newTag, setNewTag] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (agent) {
      setConfig(agent);
    }
  }, [agent]);

  const validateConfig = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!config.name.trim()) {
      newErrors.name = '智能体名称不能为空';
    }

    if (!config.description.trim()) {
      newErrors.description = '智能体描述不能为空';
    }

    if (!config.model) {
      newErrors.model = '请选择一个模型';
    }

    if (config.temperature < 0 || config.temperature > 2) {
      newErrors.temperature = '温度值必须在 0-2 之间';
    }

    if (config.maxTokens < 1 || config.maxTokens > 8192) {
      newErrors.maxTokens = '最大令牌数必须在 1-8192 之间';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (validateConfig()) {
      onSave(config);
    }
  };

  const addCapability = () => {
    if (newCapability && !config.capabilities.includes(newCapability)) {
      setConfig(prev => ({
        ...prev,
        capabilities: [...prev.capabilities, newCapability]
      }));
      setNewCapability('');
    }
  };

  const removeCapability = (capability: string) => {
    setConfig(prev => ({
      ...prev,
      capabilities: prev.capabilities.filter(c => c !== capability)
    }));
  };

  const addTag = () => {
    if (newTag && !config.tags.includes(newTag)) {
      setConfig(prev => ({
        ...prev,
        tags: [...prev.tags, newTag]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tag: string) => {
    setConfig(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }));
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Brain className="h-6 w-6 text-blue-600" />
          <h1 className="text-2xl font-bold">
            {agent ? '编辑智能体' : '创建智能体'}
          </h1>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            取消
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? '保存中...' : '保存'}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">基本信息</TabsTrigger>
          <TabsTrigger value="model">模型配置</TabsTrigger>
          <TabsTrigger value="capabilities">能力设置</TabsTrigger>
          <TabsTrigger value="advanced">高级设置</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>基本信息</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">智能体名称 *</Label>
                  <Input
                    id="name"
                    value={config.name}
                    onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="输入智能体名称"
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500">{errors.name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="isActive">状态</Label>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isActive"
                      checked={config.isActive}
                      onCheckedChange={(checked) => setConfig(prev => ({ ...prev, isActive: checked }))}
                    />
                    <span className="text-sm">
                      {config.isActive ? '启用' : '禁用'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">描述 *</Label>
                <Textarea
                  id="description"
                  value={config.description}
                  onChange={(e) => setConfig(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="描述智能体的功能和用途"
                  rows={3}
                  className={errors.description ? 'border-red-500' : ''}
                />
                {errors.description && (
                  <p className="text-sm text-red-500">{errors.description}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="tags">标签</Label>
                <div className="flex space-x-2">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="添加标签"
                    onKeyPress={(e) => e.key === 'Enter' && addTag()}
                  />
                  <Button type="button" onClick={addTag} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {config.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="model" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="h-5 w-5" />
                <span>模型配置</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="model">模型 *</Label>
                  <Select
                    value={config.model}
                    onValueChange={(value) => setConfig(prev => ({ ...prev, model: value }))}
                  >
                    <SelectTrigger className={errors.model ? 'border-red-500' : ''}>
                      <SelectValue placeholder="选择模型" />
                    </SelectTrigger>
                    <SelectContent>
                      {AVAILABLE_MODELS.map((model) => (
                        <SelectItem key={model.value} value={model.value}>
                          {model.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.model && (
                    <p className="text-sm text-red-500">{errors.model}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="temperature">温度 (0-2)</Label>
                  <Input
                    id="temperature"
                    type="number"
                    min="0"
                    max="2"
                    step="0.1"
                    value={config.temperature}
                    onChange={(e) => setConfig(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
                    className={errors.temperature ? 'border-red-500' : ''}
                  />
                  {errors.temperature && (
                    <p className="text-sm text-red-500">{errors.temperature}</p>
                  )}
                  <p className="text-xs text-gray-500">
                    控制输出的随机性，值越高越随机
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxTokens">最大令牌数</Label>
                  <Input
                    id="maxTokens"
                    type="number"
                    min="1"
                    max="8192"
                    value={config.maxTokens}
                    onChange={(e) => setConfig(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
                    className={errors.maxTokens ? 'border-red-500' : ''}
                  />
                  {errors.maxTokens && (
                    <p className="text-sm text-red-500">{errors.maxTokens}</p>
                  )}
                  <p className="text-xs text-gray-500">
                    限制单次响应的最大长度
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="systemPrompt">系统提示词</Label>
                <Textarea
                  id="systemPrompt"
                  value={config.systemPrompt}
                  onChange={(e) => setConfig(prev => ({ ...prev, systemPrompt: e.target.value }))}
                  placeholder="定义智能体的角色、行为和约束..."
                  rows={6}
                />
                <p className="text-xs text-gray-500">
                  系统提示词将指导智能体的行为和响应风格
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="capabilities" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-5 w-5" />
                <span>能力设置</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>添加能力</Label>
                <div className="flex space-x-2">
                  <Select value={newCapability} onValueChange={setNewCapability}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择能力" />
                    </SelectTrigger>
                    <SelectContent>
                      {DEFAULT_CAPABILITIES.filter(cap => !config.capabilities.includes(cap)).map((capability) => (
                        <SelectItem key={capability} value={capability}>
                          {capability}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button type="button" onClick={addCapability} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label>当前能力</Label>
                <div className="flex flex-wrap gap-2">
                  {config.capabilities.map((capability) => (
                    <Badge key={capability} variant="secondary" className="flex items-center gap-1">
                      {capability}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeCapability(capability)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>

              {config.capabilities.length === 0 && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    建议为智能体添加至少一个能力以确保正常工作。
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>高级设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>元数据</Label>
                <Textarea
                  value={JSON.stringify(config.metadata, null, 2)}
                  onChange={(e) => {
                    try {
                      const metadata = JSON.parse(e.target.value);
                      setConfig(prev => ({ ...prev, metadata }));
                    } catch (error) {
                      // 忽略无效的 JSON
                    }
                  }}
                  placeholder='{"key": "value"}'
                  rows={4}
                />
                <p className="text-xs text-gray-500">
                  以 JSON 格式存储额外的配置信息
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
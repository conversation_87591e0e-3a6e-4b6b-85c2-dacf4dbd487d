'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  Bot, 
  Settings, 
  Users, 
  BarChart3, 
  Plus,
  Play,
  Pause,
  Square,
  RefreshCw
} from 'lucide-react';
import { AgentConfigPanel } from '@/components/agents/AgentConfigPanel';
import { CollaborationMonitor } from '@/components/agents/CollaborationMonitor';
import { PerformanceAnalytics } from '@/components/agents/PerformanceAnalytics';
import { AgentList } from '@/components/agents/AgentList';

interface Agent {
  id: string;
  name: string;
  role: string;
  status: 'active' | 'inactive' | 'busy' | 'error';
  capabilities: string[];
  performance: {
    tasksCompleted: number;
    successRate: number;
    avgResponseTime: number;
  };
  lastActivity: string;
}

interface CollaborationSession {
  id: string;
  name: string;
  participants: string[];
  status: 'active' | 'completed' | 'paused';
  progress: number;
  startTime: string;
}

export default function AgentsPage() {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [collaborationSessions, setCollaborationSessions] = useState<CollaborationSession[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);

  // 模拟数据加载
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      
      // 模拟API调用
      setTimeout(() => {
        setAgents([
          {
            id: 'agent-1',
            name: '代码分析助手',
            role: 'code_analyst',
            status: 'active',
            capabilities: ['代码审查', '性能分析', '安全检测'],
            performance: {
              tasksCompleted: 156,
              successRate: 94.5,
              avgResponseTime: 2.3
            },
            lastActivity: '2分钟前'
          },
          {
            id: 'agent-2',
            name: '内容创作专家',
            role: 'content_creator',
            status: 'busy',
            capabilities: ['文档生成', '技术写作', '多语言翻译'],
            performance: {
              tasksCompleted: 89,
              successRate: 97.2,
              avgResponseTime: 4.1
            },
            lastActivity: '正在执行任务'
          },
          {
            id: 'agent-3',
            name: '数据科学家',
            role: 'data_scientist',
            status: 'inactive',
            capabilities: ['数据分析', '机器学习', '可视化'],
            performance: {
              tasksCompleted: 234,
              successRate: 91.8,
              avgResponseTime: 5.7
            },
            lastActivity: '1小时前'
          }
        ]);

        setCollaborationSessions([
          {
            id: 'session-1',
            name: '代码重构项目',
            participants: ['agent-1', 'agent-2'],
            status: 'active',
            progress: 65,
            startTime: '2小时前'
          },
          {
            id: 'session-2',
            name: '数据分析报告',
            participants: ['agent-3'],
            status: 'completed',
            progress: 100,
            startTime: '昨天'
          }
        ]);

        setIsLoading(false);
      }, 1000);
    };

    loadData();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'busy': return 'bg-yellow-500';
      case 'inactive': return 'bg-gray-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '活跃';
      case 'busy': return '忙碌';
      case 'inactive': return '空闲';
      case 'error': return '错误';
      default: return '未知';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-6 w-6 animate-spin" />
          <span>加载智能体数据中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题和操作按钮 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">智能体管理</h1>
          <p className="text-muted-foreground">管理和监控CAMEL智能体框架</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            创建智能体
          </Button>
        </div>
      </div>

      {/* 概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总智能体数</CardTitle>
            <Bot className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{agents.length}</div>
            <p className="text-xs text-muted-foreground">
              {agents.filter(a => a.status === 'active').length} 个活跃
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">协作会话</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{collaborationSessions.length}</div>
            <p className="text-xs text-muted-foreground">
              {collaborationSessions.filter(s => s.status === 'active').length} 个进行中
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均成功率</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(agents.reduce((sum, agent) => sum + agent.performance.successRate, 0) / agents.length).toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              过去30天
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">系统状态</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">正常</div>
            <p className="text-xs text-muted-foreground">
              所有服务运行正常
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="agents">智能体管理</TabsTrigger>
          <TabsTrigger value="collaboration">协作监控</TabsTrigger>
          <TabsTrigger value="analytics">性能分析</TabsTrigger>
          <TabsTrigger value="config">配置</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 智能体状态概览 */}
            <Card>
              <CardHeader>
                <CardTitle>智能体状态</CardTitle>
                <CardDescription>当前所有智能体的运行状态</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {agents.map((agent) => (
                    <div key={agent.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${getStatusColor(agent.status)}`} />
                        <div>
                          <p className="font-medium">{agent.name}</p>
                          <p className="text-sm text-muted-foreground">{agent.role}</p>
                        </div>
                      </div>
                      <Badge variant="outline">
                        {getStatusText(agent.status)}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 协作会话概览 */}
            <Card>
              <CardHeader>
                <CardTitle>协作会话</CardTitle>
                <CardDescription>当前进行中的协作任务</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {collaborationSessions.map((session) => (
                    <div key={session.id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <p className="font-medium">{session.name}</p>
                        <Badge variant={session.status === 'active' ? 'default' : 'secondary'}>
                          {session.status === 'active' ? '进行中' : '已完成'}
                        </Badge>
                      </div>
                      <Progress value={session.progress} className="h-2" />
                      <p className="text-sm text-muted-foreground">
                        参与者: {session.participants.length} 个智能体 • 开始于 {session.startTime}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="agents">
          <AgentList 
            agents={agents} 
            onAgentSelect={setSelectedAgent}
            onAgentUpdate={(updatedAgent) => {
              setAgents(agents.map(a => a.id === updatedAgent.id ? updatedAgent : a));
            }}
          />
        </TabsContent>

        <TabsContent value="collaboration">
          <CollaborationMonitor 
            sessions={collaborationSessions}
            agents={agents}
          />
        </TabsContent>

        <TabsContent value="analytics">
          <PerformanceAnalytics agents={agents} />
        </TabsContent>

        <TabsContent value="config">
          <AgentConfigPanel 
            agent={selectedAgent ? {
              id: selectedAgent.id,
              name: selectedAgent.name,
              description: `${selectedAgent.role} 智能体`,
              model: 'gpt-4',
              temperature: 0.7,
              maxTokens: 2000,
              systemPrompt: `你是一个${selectedAgent.role}智能体，具备以下能力：${selectedAgent.capabilities.join(', ')}`,
              capabilities: selectedAgent.capabilities,
              isActive: selectedAgent.status === 'active',
              tags: [selectedAgent.role],
              metadata: {
                performance: selectedAgent.performance,
                lastActivity: selectedAgent.lastActivity
              }
            } : undefined}
            onSave={(config) => {
              console.log('配置保存:', config);
            }}
            onCancel={() => {
              console.log('取消配置');
            }}
          />
        </TabsContent>
=======
      </Tabs>
    </div>
  );
}
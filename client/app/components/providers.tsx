'use client';

import React from 'react';
import { ThemeProvider as NextThemesProvider } from '@/components/theme-provider';
import RouteGuard from '@/components/RouteGuard';
import { Toaster } from '@/components/ui/toaster';
import ClientConditional from '@/components/client/ClientConditional';
import { AuthProvider } from '@/contexts/auth-context';
import { NotificationProvider } from '@/contexts/notification-context';

/**
 * 应用提供者组件
 * 
 * 将所有客户端提供者组件集中在一起，避免在服务器组件中直接导入客户端组件
 */
export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="dark"
      enableSystem
      disableTransitionOnChange
      storageKey="sgaspace-theme"
    >
      <NotificationProvider>
        <AuthProvider>
          <RouteGuard />
          <ClientConditional>
            {children}
          </ClientConditional>
        </AuthProvider>
      </NotificationProvider>
      <Toaster />
    </NextThemesProvider>
  );
} 
'use client'

import "./globals.css"
import { Inter } from "next/font/google"
import { Providers } from "./components/providers"
import { Toaster } from '@/components/ui/toaster'
import { AuthProvider } from '@/contexts/auth-context'

const inter = Inter({ subsets: ["latin"] })

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body className={inter.className} suppressHydrationWarning>
        <AuthProvider>
          <Providers>
            {children}
          </Providers>
          <Toaster />
        </AuthProvider>
      </body>
    </html>
  )
} 
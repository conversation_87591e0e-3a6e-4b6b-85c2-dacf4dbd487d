import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

/**
 * API代理中转路由
 * 将客户端API请求转发到后端，避免跨域问题
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const path = (await params).path.join('/');
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';
  const baseUrl = apiUrl.replace(/\/api\/v1$/, '');
  
  // 构建完整的URL
  const fullUrl = `${apiUrl}/${path}`;
  
  console.log(`[API代理] 转发GET请求到:`, fullUrl);
  
  try {
    // 获取原始请求中的查询参数
    const { searchParams } = new URL(request.url);
    
    // 创建新的URL以包含查询参数
    const urlWithQuery = new URL(fullUrl);
    searchParams.forEach((value, key) => {
      urlWithQuery.searchParams.append(key, value);
    });
    
    // 从原始请求中获取标头
    const headers = new Headers();
    request.headers.forEach((value, key) => {
      // 跳过特定的标头
      if (!['host', 'connection'].includes(key.toLowerCase())) {
        headers.append(key, value);
      }
    });
    
    // 转发请求
    const response = await fetch(urlWithQuery.toString(), {
      method: 'GET',
      headers,
      credentials: 'include',
    });
    
    // 创建NextResponse
    const data = await response.json();
    return NextResponse.json(data, {
      status: response.status,
      statusText: response.statusText,
    });
  } catch (error) {
    console.error(`[API代理] 转发请求失败:`, error);
    return NextResponse.json(
      { error: '代理请求失败', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const path = (await params).path.join('/');
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';
  const baseUrl = apiUrl.replace(/\/api$/, '');
  
  // 构建完整的URL
  const fullUrl = `${baseUrl}/${path}`;
  
  console.log(`[API代理] 转发POST请求到:`, fullUrl);
  
  try {
    // 获取原始请求体
    let body;
    const contentType = request.headers.get('content-type');
    
    if (contentType?.includes('application/json')) {
      body = await request.json();
    } else if (contentType?.includes('application/x-www-form-urlencoded')) {
      body = await request.formData();
    } else {
      body = await request.text();
    }
    
    // 从原始请求中获取标头
    const headers = new Headers();
    request.headers.forEach((value, key) => {
      // 跳过特定的标头
      if (!['host', 'connection'].includes(key.toLowerCase())) {
        headers.append(key, value);
      }
    });
    
    // 转发请求
    const response = await fetch(fullUrl, {
      method: 'POST',
      headers,
      body: typeof body === 'string' ? body : JSON.stringify(body),
      credentials: 'include',
    });
    
    // 创建NextResponse
    const data = await response.json();
    return NextResponse.json(data, {
      status: response.status,
      statusText: response.statusText,
    });
  } catch (error) {
    console.error(`[API代理] 转发请求失败:`, error);
    return NextResponse.json(
      { error: '代理请求失败', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

// 支持其他HTTP方法
export async function PUT(
  request: NextRequest, 
  { params }: { params: Promise<{ path: string[] }> }
) {
  return forwardRequest(request, (await params).path, 'PUT');
}

export async function DELETE(
  request: NextRequest, 
  { params }: { params: Promise<{ path: string[] }> }
) {
  return forwardRequest(request, (await params).path, 'DELETE');
}

export async function PATCH(
  request: NextRequest, 
  { params }: { params: Promise<{ path: string[] }> }
) {
  return forwardRequest(request, (await params).path, 'PATCH');
}

// 通用请求转发函数
async function forwardRequest(
  request: NextRequest,
  pathArray: string[],
  method: string
) {
  const path = pathArray.join('/');
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';
  const baseUrl = apiUrl.replace(/\/api$/, '');
  
  // 构建完整的URL
  const fullUrl = `${baseUrl}/${path}`;
  
  console.log(`[API代理] 转发${method}请求到:`, fullUrl);
  
  try {
    // 获取原始请求体
    let body;
    const contentType = request.headers.get('content-type');
    
    if (contentType?.includes('application/json')) {
      body = await request.json();
    } else if (contentType?.includes('application/x-www-form-urlencoded')) {
      body = await request.formData();
    } else {
      body = await request.text();
    }
    
    // 从原始请求中获取标头
    const headers = new Headers();
    request.headers.forEach((value, key) => {
      // 跳过特定的标头
      if (!['host', 'connection'].includes(key.toLowerCase())) {
        headers.append(key, value);
      }
    });
    
    // 转发请求
    const response = await fetch(fullUrl, {
      method,
      headers,
      body: typeof body === 'string' ? body : JSON.stringify(body),
      credentials: 'include',
    });
    
    // 创建NextResponse
    const data = await response.json();
    return NextResponse.json(data, {
      status: response.status,
      statusText: response.statusText,
    });
  } catch (error) {
    console.error(`[API代理] 转发请求失败:`, error);
    return NextResponse.json(
      { error: '代理请求失败', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
} 
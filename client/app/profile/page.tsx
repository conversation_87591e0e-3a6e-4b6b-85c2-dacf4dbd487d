"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { useNotification } from "@/context/notification-context";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { userApi, authApi } from "@/lib/api";
import { safeAsync } from "@/lib/api-error-handler";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { ArrowLeft, Loader2, Save } from "lucide-react";

// 个人资料表单验证模式
const profileFormSchema = z.object({
  name: z.string().min(2, {
    message: "用户名至少需要2个字符",
  }),
  email: z.string().email({
    message: "请输入有效的电子邮件地址",
  }),
});

// 密码修改表单验证模式
const passwordFormSchema = z.object({
  currentPassword: z.string().min(8, {
    message: "当前密码至少需要8个字符",
  }),
  newPassword: z.string().min(8, {
    message: "新密码至少需要8个字符",
  }),
  confirmPassword: z.string().min(8, {
    message: "确认密码至少需要8个字符",
  }),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "新密码和确认密码不匹配",
  path: ["confirmPassword"],
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;
type PasswordFormValues = z.infer<typeof passwordFormSchema>;

export default function ProfilePage() {
  const { user } = useAuth();
  const { success, error, handleError } = useNotification();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [passwordLoading, setPasswordLoading] = useState(false);

  // 个人资料表单
  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: "",
      email: "",
    },
  });

  // 密码修改表单
  const passwordForm = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  // 加载用户资料
  useEffect(() => {
    if (user) {
      profileForm.setValue("name", user.name || "");
      profileForm.setValue("email", user.email);
    }
  }, [user, profileForm]);

  // 提交个人资料表单
  const onProfileSubmit = async (data: ProfileFormValues) => {
    try {
      const result = await safeAsync(
        userApi.updateProfile({
          name: data.name,
          email: data.email,
        }),
        setLoading,
        handleError
      );

      if (result?.data) {
        success({ description: "个人资料已成功更新" });
      }
    } catch (err) {
      handleError(err);
    }
  };

  // 提交密码修改表单
  const onPasswordSubmit = async (data: PasswordFormValues) => {
    try {
      const result = await safeAsync(
        authApi.changePassword(
          data.currentPassword,
          data.newPassword,
          data.confirmPassword
        ),
        setPasswordLoading,
        handleError
      );

      if (result?.data) {
        success({ description: "密码已成功更新" });
        passwordForm.reset();
      }
    } catch (err) {
      handleError(err);
    }
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/dashboard")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">个人资料设置</h1>
        </div>

        {/* 个人资料卡片 */}
        <Card>
          <CardHeader>
            <CardTitle>个人资料</CardTitle>
            <CardDescription>
              更新您的个人信息和联系方式
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...profileForm}>
              <form
                onSubmit={profileForm.handleSubmit(onProfileSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={profileForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>用户名</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入您的用户名" {...field} />
                      </FormControl>
                      <FormDescription>
                        这是您在平台上显示的名称
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={profileForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>电子邮件</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="请输入您的电子邮件"
                          type="email"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        这是您的登录邮箱，更改后需要重新验证
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button type="submit" disabled={loading}>
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      保存中...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      保存更改
                    </>
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* 密码修改卡片 */}
        <Card>
          <CardHeader>
            <CardTitle>修改密码</CardTitle>
            <CardDescription>
              更新您的账户密码以保护账户安全
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...passwordForm}>
              <form
                onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={passwordForm.control}
                  name="currentPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>当前密码</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="请输入当前密码"
                          type="password"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={passwordForm.control}
                  name="newPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>新密码</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="请输入新密码"
                          type="password"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        密码至少需要8个字符
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={passwordForm.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>确认新密码</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="请再次输入新密码"
                          type="password"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button type="submit" disabled={passwordLoading}>
                  {passwordLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      更新中...
                    </>
                  ) : (
                    "更新密码"
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 
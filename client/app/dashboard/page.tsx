"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { useNotification } from "@/context/notification-context";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { tenantApi, authApi } from "@/lib/api";
import { Loader2, LogOut, User, Building } from "lucide-react";
import { Tenant, UserTenant } from "@/types/user";
import { safeAsync } from "@/lib/api-error-handler";
import axios from "axios";

export default function DashboardPage() {
  const { user, logout } = useAuth();
  const { success, handleError } = useNotification();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [tenants, setTenants] = useState<UserTenant[]>([]);
  const [selectedTenantId, setSelectedTenantId] = useState<string | null>(null);

  // 加载用户租户
  useEffect(() => {
    const loadTenants = async () => {
      try {
        setLoading(true);
        
        // 直接使用axios调用API，避免使用safeAsync
        const response = await axios.get('/api/proxy/tenants/my');
        
        // 添加额外的调试日志
        console.log('租户API响应:', response);
        
        // 确保API返回了数据
        if (!response || !response.data) {
          console.log('未获取到租户数据');
          setLoading(false);
          return;
        }
        
        // 后端API返回的格式是 [{"tenant": Tenant对象}]
        const tenantData = response.data || [];
        console.log('租户数据:', tenantData);
        
        const formattedTenants = tenantData.map((item: any) => ({
          id: item.tenant.id,
          userId: user?.id,
          tenantId: item.tenant.id,
          tenant: item.tenant,
          role: 'member', // 默认角色
          status: 'active', // 默认状态
          joinedAt: item.tenant.createdAt
        }));
        
        setTenants(formattedTenants);
        
        // 如果用户有当前租户，设置为选中
        if (user?.currentTenant) {
          setSelectedTenantId(user.currentTenant.id);
        } else if (formattedTenants.length > 0) {
          // 否则选择第一个租户
          setSelectedTenantId(formattedTenants[0].id);
        }
      } catch (error: any) {
        console.error('加载租户失败:', error);
        // 显示更详细的错误信息
        if (error.response) {
          console.error('错误响应数据:', error.response.data);
          console.error('错误状态码:', error.response.status);
        } else if (error.request) {
          console.error('请求未收到响应:', error.request);
        } else {
          console.error('错误信息:', error.message);
        }
        handleError(error);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      loadTenants();
    }
  }, [user]);

  // 切换租户
  const handleTenantChange = async (tenantId: string) => {
    if (tenantId === selectedTenantId) return;

    try {
      setLoading(true);
      
      // 直接使用axios调用API
      const response = await axios.post('/api/proxy/auth/switch-tenant', { tenant_id: tenantId });
      
      if (response && response.data) {
        setSelectedTenantId(tenantId);
        success({ description: "已切换到新的工作空间" });

        // 刷新页面以更新用户上下文
        window.location.reload();
      }
    } catch (error: any) {
      console.error('切换租户失败:', error);
      // 显示更详细的错误信息
      if (error.response) {
        console.error('错误响应数据:', error.response.data);
        console.error('错误状态码:', error.response.status);
      } else if (error.request) {
        console.error('请求未收到响应:', error.request);
      } else {
        console.error('错误信息:', error.message);
      }
      handleError(error);
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl">欢迎回来，{user.name || user.email}</CardTitle>
              <CardDescription>您已成功登录到SGASpace智能工作台</CardDescription>
            </div>
            <Button variant="outline" size="icon" onClick={logout}>
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 租户选择器 */}
          <div className="space-y-2">
            <div className="text-sm font-medium">当前工作空间</div>
            <Select
              value={selectedTenantId || ""}
              onValueChange={handleTenantChange}
              disabled={loading}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="选择工作空间" />
              </SelectTrigger>
              <SelectContent>
                {tenants.map((tenant) => (
                  <SelectItem key={tenant.tenantId} value={tenant.tenantId}>
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4 text-muted-foreground" />
                      <span>{tenant.tenant.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 用户信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">个人信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">用户名：</span>
                  <span>{user.name || '未设置'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="h-4 w-4 text-muted-foreground">@</span>
                  <span className="text-sm font-medium">邮箱：</span>
                  <span>{user.email}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">账户状态：</span>
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    user.status === 'active' ? 'bg-green-100 text-green-800' :
                    user.status === 'pending' ? 'bg-amber-100 text-amber-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {user.status === 'active' ? '已激活' :
                     user.status === 'pending' ? '待验证' :
                     user.status === 'suspended' ? '已停用' : '未知'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 快速链接 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Button onClick={() => router.push('/profile')}>
              管理个人资料
            </Button>
            <Button onClick={() => router.push('/test-notifications')} variant="outline">
              测试通知系统
            </Button>
            <Button onClick={() => router.push('/agent')} variant="outline">
              智能助手
            </Button>
            <Button onClick={() => router.push('/models')} variant="outline">
              模型管理
            </Button>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <p className="text-sm text-muted-foreground">
            登录时间: {new Date().toLocaleString()}
          </p>
        </CardFooter>
      </Card>
    </div>
  );
} 
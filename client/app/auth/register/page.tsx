"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { AuthCard } from "@/components/ui/auth-card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertCircle } from "lucide-react";
import { useAuth } from "@/contexts/auth-context";

export default function RegisterPage() {
  const { register, error: authError, clearError, isLoading } = useAuth();
  const [fullName, setFullName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [passwordConfirm, setPasswordConfirm] = useState("");
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [localError, setLocalError] = useState("");

  // 清除认证上下文中的错误
  useEffect(() => {
    clearError();
  }, [clearError]);

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setLocalError("");

    // 验证密码
    if (password !== passwordConfirm) {
      setLocalError("两次输入的密码不匹配");
      return;
    }

    // 验证条款接受
    if (!acceptTerms) {
      setLocalError("请接受服务条款");
      return;
    }

    try {
      await register(email, password, fullName);
    } catch (err: any) {
      setLocalError(err.message || "注册失败，请重试");
    }
  }

  return (
    <AuthCard
      title="创建账户"
      description="注册新的SGASpace账户"
      footer={
        <div className="text-center text-sm text-gray-400">
          已有账号？{" "}
          <Link href="/auth/login" className="text-blue-500 hover:underline">
            登录
          </Link>
        </div>
      }
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {(authError || localError) && (
          <div className="bg-red-900/20 border border-red-800 text-red-500 px-4 py-2 rounded-md text-sm flex items-center">
            <AlertCircle className="h-4 w-4 mr-2" />
            {authError || localError}
          </div>
        )}
        <div className="space-y-2">
          <Label htmlFor="fullName">姓名</Label>
          <Input
            id="fullName"
            placeholder="您的姓名"
            value={fullName}
            onChange={(e) => setFullName(e.target.value)}
            required
            className="bg-[#1e1e1e] border-gray-800"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">邮箱</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="bg-[#1e1e1e] border-gray-800"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="password">密码</Label>
          <Input
            id="password"
            type="password"
            placeholder="••••••••"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            minLength={8}
            className="bg-[#1e1e1e] border-gray-800"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="passwordConfirm">确认密码</Label>
          <Input
            id="passwordConfirm"
            type="password"
            placeholder="••••••••"
            value={passwordConfirm}
            onChange={(e) => setPasswordConfirm(e.target.value)}
            required
            minLength={8}
            className="bg-[#1e1e1e] border-gray-800"
          />
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox 
            id="terms" 
            checked={acceptTerms}
            onCheckedChange={(checked) => setAcceptTerms(checked as boolean)}
          />
          <Label htmlFor="terms" className="text-sm font-normal">
            我同意
            <Link href="/terms" className="text-blue-500 hover:underline ml-1">
              服务条款
            </Link>
            和
            <Link href="/privacy" className="text-blue-500 hover:underline ml-1">
              隐私政策
            </Link>
          </Label>
        </div>
        <Button
          type="submit"
          className="w-full bg-blue-600 hover:bg-blue-700"
          disabled={isLoading}
        >
          {isLoading ? "注册中..." : "注册"}
        </Button>
      </form>
    </AuthCard>
  );
} 
"use client";

import React, { useState, useEffect, Suspense } from "react";
import Link from "next/link";
import { AuthCard } from "@/components/ui/auth-card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertCircle, Mail, Lock, Eye, EyeOff } from "lucide-react";
import { useAuth } from "@/contexts/auth-context";
import { motion } from "framer-motion";
import { useRouter, useSearchParams } from 'next/navigation'
import { toast } from '@/components/ui/use-toast'
import { Loader2 } from 'lucide-react'

function LoginForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { login, error: authError, clearError, isLoading } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [localError, setLocalError] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [formTouched, setFormTouched] = useState(false);
  const [loading, setLoading] = useState(false);

  // 清除认证上下文中的错误
  useEffect(() => {
    clearError();
  }, [clearError]);

  // 验证邮箱格式
  const isEmailValid = () => {
    if (!email) return true; // 空值不显示错误
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // 验证密码强度
  const isPasswordValid = () => {
    return !password || password.length >= 6; // 空值或长度至少为6
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || !password) {
      toast({
        title: '请输入邮箱和密码',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      await login(email, password);
      const from = searchParams.get('from') || '/dashboard';
      router.push(from);
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="flex flex-col items-center">
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full"
      >
        <AuthCard
          title="登录到SGASpace"
          description="欢迎回来，请输入您的凭据"
          footer={
            <div className="text-center text-sm text-gray-400">
              还没有账号？{" "}
              <Link href="/auth/register" className="text-blue-500 hover:underline transition-colors">
                立即注册
              </Link>
            </div>
          }
        >
        <form onSubmit={handleSubmit} className="space-y-5">
          {(authError || localError) && (
            <motion.div 
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              transition={{ duration: 0.3 }}
              className="bg-red-900/20 border border-red-800 text-red-500 px-4 py-3 rounded-md text-sm flex items-center"
            >
              <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
              <span>{authError || localError}</span>
            </motion.div>
          )}
          
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium">邮箱</Label>
            <div className="relative">
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                <Mail className="h-4 w-4" />
              </div>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  setFormTouched(true);
                }}
                required
                className={`bg-[#1e1e1e] border-gray-800 pl-10 focus:border-blue-500 transition-colors ${
                  formTouched && !isEmailValid() ? "border-red-500" : ""
                }`}
              />
            </div>
            {formTouched && !isEmailValid() && (
              <p className="text-xs text-red-500 mt-1">请输入有效的电子邮箱地址</p>
            )}
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label htmlFor="password" className="text-sm font-medium">密码</Label>
              <Link
                href="/auth/forgot-password"
                className="text-xs text-blue-500 hover:text-blue-400 transition-colors"
              >
                忘记密码？
              </Link>
            </div>
            <div className="relative">
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                <Lock className="h-4 w-4" />
              </div>
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="••••••••"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  setFormTouched(true);
                }}
                required
                className={`bg-[#1e1e1e] border-gray-800 pl-10 pr-10 focus:border-blue-500 transition-colors ${
                  formTouched && !isPasswordValid() ? "border-red-500" : ""
                }`}
              />
              <button
                type="button"
                onClick={togglePasswordVisibility}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors"
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {formTouched && !isPasswordValid() && (
              <p className="text-xs text-red-500 mt-1">密码长度至少为6个字符</p>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="remember" 
              checked={rememberMe}
              onCheckedChange={(checked) => setRememberMe(checked as boolean)}
              className="data-[state=checked]:bg-blue-600"
            />
            <Label htmlFor="remember" className="text-sm font-normal cursor-pointer">
              记住我
            </Label>
          </div>
          
          <Button
            type="submit"
            className="w-full bg-blue-600 hover:bg-blue-700 transition-colors h-11 font-medium"
            disabled={loading}
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                登录中...
              </div>
            ) : (
              "登录"
            )}
          </Button>
        </form>
        </AuthCard>
        
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="mt-6 text-center"
        >
          <div className="flex justify-center items-center mb-3">
            <h2 className="text-2xl font-bold text-white">SGASpace 智能工作台</h2>
          </div>
          
          <div className="border-t border-gray-800 mt-2 pt-4 pb-2">
            <p className="text-xs text-gray-500 flex justify-center items-center gap-1">
              <span>© {new Date().getFullYear()} SGASpace.</span>
              <span>保留所有权利。</span>
              <span className="hidden sm:inline mx-1">|</span>
              <a href="/terms" className="text-gray-500 hover:text-blue-400 transition-colors hidden sm:inline">用户协议</a>
              <span className="hidden sm:inline mx-1">|</span>
              <a href="/privacy" className="text-gray-500 hover:text-blue-400 transition-colors hidden sm:inline">隐私政策</a>
            </p>
          </div>
        </motion.div>
      </motion.div>
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="w-full text-center mt-6 mb-8 p-4 bg-gray-900 rounded-lg border border-gray-800"
      >
        <h3 className="text-base font-semibold mb-2 text-white">SGASpace 智能工作台</h3>
        
        <div className="mb-3">
          <p className="text-xs text-gray-200 mb-2">
            欢迎使用 SGA Space 智能工作平台
          </p>
          <p className="text-xs text-gray-200">
            SGA Space 是一个集成了多种AI模型和工具的智能工作平台，帮助您更高效地完成工作。
          </p>
        </div>
        
        <div className="text-left">
          <div className="flex items-center mb-1">
            <span className="text-green-500 mr-1">✓</span>
            <p className="text-xs text-gray-200">多模型集成，满足不同场景需求</p>
          </div>
          <div className="flex items-center mb-1">
            <span className="text-green-500 mr-1">✓</span>
            <p className="text-xs text-gray-200">智能工作流，自动化处理复杂任务</p>
          </div>
          <div className="flex items-center mb-1">
            <span className="text-green-500 mr-1">✓</span>
            <p className="text-xs text-gray-200">多租户架构，安全隔离数据</p>
          </div>
          <div className="flex items-center">
            <span className="text-green-500 mr-1">✓</span>
            <p className="text-xs text-gray-200">丰富的API接口，轻松集成现有系统</p>
          </div>
        </div>
      </motion.div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen"><Loader2 className="h-8 w-8 animate-spin" /></div>}>
      <LoginForm />
    </Suspense>
  );
}

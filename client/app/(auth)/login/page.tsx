"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { useNotification } from '@/contexts/notification-context';

const LoginPage: React.FC = () => {
  const router = useRouter();
  const { login, isLoading, error: authError } = useAuth();
  const { addNotification } = useNotification();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [localLoading, setLocalLoading] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('');

  // 输出调试信息
  useEffect(() => {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';
    console.log('当前API地址:', apiUrl);
    console.log('认证状态:', { isLoading, authError });
    
    // 检查token是否存在
    const hasToken = !!localStorage.getItem('token');
    console.log('本地存储中的token:', hasToken ? '存在' : '不存在');
    
    setDebugInfo(`API: ${apiUrl}, Token: ${hasToken ? '存在' : '不存在'}`);
  }, [isLoading, authError]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLocalLoading(true);
    
    try {
      console.log('提交登录表单...', { email, password: '***' });
      await login(email, password);
      console.log('登录成功，准备跳转...');
      
      addNotification({
        type: 'success',
        title: '登录成功',
        message: '欢迎回来！'
      });
      
      // 检查是否有登录后重定向
      const redirectPath = sessionStorage.getItem('redirectAfterLogin');
      if (redirectPath) {
        console.log('登录后重定向到:', redirectPath);
        sessionStorage.removeItem('redirectAfterLogin');
        router.push(redirectPath);
      } else {
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('登录提交失败:', error);
      
      // 显示错误通知
      let errorMessage = '登录失败，请重试';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null) {
        errorMessage = JSON.stringify(error);
      }
      
      console.log('错误详情:', errorMessage);
      setDebugInfo(prev => `${prev}\n错误: ${errorMessage}`);
      
      addNotification({
        type: 'error',
        title: '登录失败',
        message: errorMessage
      });
    } finally {
      setLocalLoading(false);
    }
  };

  return (
    <div>
      {/* 添加登录表单 */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="email">邮箱</label>
          <input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="w-full p-2 border rounded"
          />
        </div>
        
        <div>
          <label htmlFor="password">密码</label>
          <input
            id="password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            className="w-full p-2 border rounded"
          />
        </div>
        
        <button
          type="submit"
          disabled={localLoading || isLoading}
          className="w-full p-2 bg-blue-500 text-white rounded"
        >
          {localLoading || isLoading ? '登录中...' : '登录'}
        </button>
      </form>
      
      {/* 调试信息 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-2 bg-gray-100 text-xs">
          <p>调试信息:</p>
          <pre>{debugInfo}</pre>
          {authError && <p className="text-red-500">认证错误: {authError}</p>}
        </div>
      )}
    </div>
  );
};

export default LoginPage; 
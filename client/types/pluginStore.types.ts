// Plugin store types
export interface PluginStoreItem {
  id: string
  name: string
  description: string
  longDescription?: string
  version: string
  latestVersion: string
  category: string
  features: string[]
  permissions: string[]
  screenshots: string[]
  icon: string
  author: string
  verified: boolean
  featured: boolean
  rating: number
  reviewCount: number
  downloadCount: number
  favoriteCount?: number
  favorited: boolean
  size?: string
  updatedAt?: string
  homepage?: string
}

export interface PluginCategory {
  id: string
  name: string
  description: string
}
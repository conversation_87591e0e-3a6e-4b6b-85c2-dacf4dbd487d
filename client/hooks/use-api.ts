/**
 * API钩子
 * 
 * 封装API调用，处理认证和错误管理
 */
"use client";

import { useState } from 'react';
import axios, { AxiosRequestConfig, InternalAxiosRequestConfig } from 'axios';
import { useAuth } from '@/contexts/auth-context';
import { useNotification } from '@/context/notification-context';

// 检查是否是开发环境
const isDevelopment = process.env.NODE_ENV === 'development';

// 创建axios实例
const axiosInstance = axios.create({
  baseURL: isDevelopment ? '/api/proxy' : (process.env.NEXT_PUBLIC_API_URL || '/api'),
  headers: {
    'Content-Type': 'application/json',
  },
});

export function useApi() {
  const { token, logout } = useAuth();
  const { handleError } = useNotification();
  
  // 添加认证信息
  axiosInstance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      
      // 开发环境调试日志
      if (isDevelopment) {
        console.log(`[API请求] ${config.method?.toUpperCase()} ${config.url}`, config.data || '');
      }
      
      return config;
    },
    (error) => Promise.reject(error)
  );
  
  // 处理响应错误
  axiosInstance.interceptors.response.use(
    (response) => {
      // 开发环境调试日志
      if (isDevelopment) {
        console.log(`[API响应] ${response.status} ${response.config.url}`, response.data);
      }
      return response;
    },
    (error) => {
      // 开发环境调试日志
      if (isDevelopment) {
        const url = error?.config?.url || '未知URL';
        const errorMsg = error?.response?.data || error?.message || '未知错误';
        console.error(`[API错误] ${url}`, errorMsg);
      }
      
      // 处理401错误（未授权）
      if (error?.response?.status === 401) {
        logout();
      }
      
      // 允许调用者处理错误
      return Promise.reject(error);
    }
  );
  
  // GET请求
  const get = async <T = any>(url: string, params?: any): Promise<T> => {
    try {
      const config: AxiosRequestConfig = {};
      if (params) {
        config.params = params;
      }
      
      const response = await axiosInstance.get<T>(url, config);
      return response.data;
    } catch (error) {
      handleError(error);
      throw error;
    }
  };
  
  // POST请求
  const post = async <T = any>(url: string, data?: any): Promise<T> => {
    try {
      const response = await axiosInstance.post<T>(url, data);
      return response.data;
    } catch (error) {
      handleError(error);
      throw error;
    }
  };
  
  // PUT请求
  const put = async <T = any>(url: string, data?: any): Promise<T> => {
    try {
      const response = await axiosInstance.put<T>(url, data);
      return response.data;
    } catch (error) {
      handleError(error);
      throw error;
    }
  };
  
  // DELETE请求
  const delete_ = async <T = any>(url: string): Promise<T> => {
    try {
      const response = await axiosInstance.delete<T>(url);
      return response.data;
    } catch (error) {
      handleError(error);
      throw error;
    }
  };
  
  // PATCH请求
  const patch = async <T = any>(url: string, data?: any): Promise<T> => {
    try {
      const response = await axiosInstance.patch<T>(url, data);
      return response.data;
    } catch (error) {
      handleError(error);
      throw error;
    }
  };
  
  return {
    get,
    post,
    put,
    delete: delete_,
    patch
  };
} 